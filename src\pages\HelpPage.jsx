import React from 'react';
import { ArrowLeft, BookOpen, MessageSquare, PenTool, Mic, Settings, User, Zap, Globe, History } from 'lucide-react';
import { useAppContext } from '../context/AppContext';

const HelpPage = () => {
  const { dispatch } = useAppContext();
  const isDarkMode = false; // 强制使用浅色主题

  const handleBack = () => {
    // 从URL参数获取来源页面
    const urlParams = new URLSearchParams(window.location.search);
    const fromPage = urlParams.get('from') || 'editor'; // 默认为editor页面
    
    // 根据来源页面返回到相应页面
    if (fromPage === 'chat') {
      dispatch({ type: 'SET_CURRENT_PAGE', payload: 'chat' });
    } else {
      dispatch({ type: 'SET_CURRENT_PAGE', payload: 'editor' });
    }
  };

  const features = [
    {
      icon: <PenTool className="w-6 h-6" />,
      title: "智能写作分析",
      description: "AI深度分析你的英文写作，提供语法、词汇、表达方式的专业建议",
      details: [
        "语法错误检测与修正",
        "词汇使用优化建议", 
        "表达方式改进",
        "写作风格分析"
      ]
    },
    {
      icon: <MessageSquare className="w-6 h-6" />,
      title: "AI对话练习",
      description: "与智能AI进行自然对话，提升口语表达能力和英语思维",
      details: [
        "自然语言对话",
        "实时表达建议",
        "上下文理解",
        "个性化学习"
      ]
    },
    {
      icon: <BookOpen className="w-6 h-6" />,
      title: "智能词典查询",
      description: "内置强大的英汉词典，支持单词查询、发音、例句",
      details: [
        "英汉双向查询",
        "发音指导",
        "丰富例句",
        "词汇收藏"
      ]
    },
    {
      icon: <Mic className="w-6 h-6" />,
      title: "语音交互",
      description: "支持语音输入和TTS播放，让英语学习更加自然",
      details: [
        "语音输入识别",
        "智能TTS播放",
        "发音纠正",
        "听力训练"
      ]
    },
    {
      icon: <History className="w-6 h-6" />,
      title: "写作历史管理",
      description: "自动保存你的写作历史，支持云端同步",
      details: [
        "自动保存历史",
        "云端同步",
        "历史回顾",
        "分享功能"
      ]
    },
    {
      icon: <User className="w-6 h-6" />,
      title: "个性化设置",
      description: "支持主题切换、头像上传等个性化功能",
      details: [
        "主题切换",
        "头像上传",
        "个性化配置",
        "用户偏好"
      ]
    }
  ];

  const quickStartSteps = [
    {
      step: "1",
      title: "写作分析",
      description: "在写作页面输入英文文本，点击右下角的\"AI分析\"按钮获取专业建议"
    },
    {
      step: "2", 
      title: "对话练习",
      description: "切换到聊天页面，与AI进行自然对话，点击你的消息获取表达建议"
    },
    {
      step: "3",
      title: "词典查询",
      description: "选中任意英文单词，系统会自动显示词典查询结果和发音"
    },
    {
      step: "4",
      title: "语音功能",
      description: "使用麦克风进行语音输入，或点击播放按钮听取AI回复"
    }
  ];

  return (
    <div 
      className="min-h-screen"
      style={{
        background: isDarkMode 
          ? 'linear-gradient(135deg, #2A241D 0%, #332B22 100%)'
          : 'linear-gradient(135deg, #F0E6D2 0%, #FFFEF7 100%)',
        color: isDarkMode ? '#E8DCC6' : '#5D4037'
      }}
    >
        {/* Header */}
        <header 
          className="sticky top-0 z-50"
          style={{
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(15px)',
            borderBottom: '1px solid #E6D7B8',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
          }}
        >
          <div className="max-w-7xl mx-auto px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 rounded-xl transition-all duration-300 hover:scale-105"
                  style={{
                    background: 'linear-gradient(135deg, #D4A574, #E6B87A)',
                    color: '#FEFCF5',
                    boxShadow: '0 4px 15px rgba(212, 165, 116, 0.3)'
                  }}
                >
                  <ArrowLeft className="w-4 h-4" />
                </button>
                <h1 
                  className="text-2xl font-bold flex items-center gap-2"
                  style={{ 
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    background: 'linear-gradient(135deg, #D4A574, #E6B87A)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}
                >
                  <span className="text-2xl">⛅</span>
                  對白 AI 使用说明
                </h1>
              </div>
            </div>
          </div>
        </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-12">
        {/* Quick Start */}
        <section className="mb-16">
          <div 
            className="rounded-3xl p-10 relative overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 230, 210, 0.8))',
              border: '2px solid #E6D7B8',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* 装饰性背景元素 */}
            <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
              <div className="w-full h-full rounded-full" style={{
                background: 'linear-gradient(135deg, #D4A574, #E6B87A)'
              }}></div>
            </div>
            
            <h2 
              className="text-4xl font-bold mb-8 text-center relative"
              style={{ 
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                background: 'linear-gradient(135deg, #D4A574, #E6B87A)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              🚀 快速开始
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              {quickStartSteps.map((step, index) => (
                <div 
                  key={index}
                  className="flex gap-6 p-8 rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-xl"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(240, 230, 210, 0.6))',
                    border: '2px solid #D4C4A8',
                    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)'
                  }}
                >
                <div 
                  className="w-16 h-16 rounded-2xl flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 shadow-lg"
                  style={{
                    background: 'linear-gradient(135deg, #D4A574, #E6B87A)',
                    boxShadow: '0 8px 25px rgba(212, 165, 116, 0.3)'
                  }}
                >
                  {step.step}
                </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold mb-3" style={{ color: '#5D4037' }}>{step.title}</h3>
                    <p className="text-lg opacity-90 leading-relaxed" style={{ color: '#8B4513' }}>{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features */}
        <section className="mb-16">
          <h2 
            className="text-4xl font-bold mb-12 text-center"
            style={{ 
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              background: 'linear-gradient(135deg, #8B4513, #D2691E)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}
          >
            💡 主要功能
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="p-8 rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl group"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 230, 210, 0.7))',
                  border: '2px solid #E6D7B8',
                  boxShadow: '0 15px 40px rgba(0, 0, 0, 0.08)'
                }}
              >
                <div className="mb-6 group-hover:scale-110 transition-transform duration-300">
                  {React.cloneElement(feature.icon, { 
                    className: "w-8 h-8",
                    style: { 
                      color: '#D4A574',
                      filter: 'drop-shadow(0 4px 8px rgba(212, 165, 116, 0.3))'
                    }
                  })}
                </div>
                <h3 className="text-2xl font-bold mb-4" style={{ color: '#5D4037' }}>{feature.title}</h3>
                <p className="mb-6 text-lg leading-relaxed opacity-90" style={{ color: '#8B4513' }}>{feature.description}</p>
                <ul className="space-y-3">
                  {feature.details.map((detail, idx) => (
                    <li key={idx} className="flex items-center gap-3 text-base">
                      <span 
                        className="w-2 h-2 rounded-full flex-shrink-0"
                        style={{ background: 'linear-gradient(135deg, #D4A574, #E6B87A)' }}
                      ></span>
                      <span style={{ color: '#5D4037' }}>{detail}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </section>


        {/* 支付套餐 */}
        <section className="mb-16">
          <div className="max-w-7xl mx-auto px-6 text-center">
            {/* 装饰性背景元素 */}
            <div className="absolute top-0 left-0 w-40 h-40 opacity-5">
              <div className="w-full h-full rounded-full" style={{
                background: 'linear-gradient(135deg, #D4A574, #E6B87A)'
              }}></div>
            </div>
            
            <h2 
              className="text-4xl font-bold mb-6"
              style={{ 
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                background: 'linear-gradient(135deg, #D4A574, #E6B87A)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              💳 按量支付套餐
            </h2>
            <p className="text-xl mb-12 opacity-90" style={{ color: '#8B4513' }}>
              选择适合你的套餐，按需付费，灵活使用
            </p>
            
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  name: '基础套餐',
                  price: '¥9.9',
                  originalPrice: '¥19.9',
                  requests: '100次',
                  description: '适合轻度使用',
                  features: [
                    '100次AI分析',
                    '100次对话建议',
                    '基础词典查询',
                    '邮件支持'
                  ],
                  popular: false,
                  color: '#D4A574'
                },
                {
                  name: '专业套餐',
                  price: '¥29.9',
                  originalPrice: '¥59.9',
                  requests: '500次',
                  description: '适合日常学习',
                  features: [
                    '500次AI分析',
                    '500次对话建议',
                    '高级词典查询',
                    '语音功能',
                    '优先支持'
                  ],
                  popular: true,
                  color: '#E6B87A'
                },
                {
                  name: '高级套餐',
                  price: '¥59.9',
                  originalPrice: '¥119.9',
                  requests: '1500次',
                  description: '适合重度使用',
                  features: [
                    '1500次AI分析',
                    '1500次对话建议',
                    '全功能词典',
                    '语音功能',
                    '历史记录同步',
                    '专属客服'
                  ],
                  popular: false,
                  color: '#D4A574'
                }
              ].map((plan, idx) => (
                <div 
                  key={idx}
                  className={`relative p-8 rounded-2xl transition-all duration-300 hover:scale-105 ${
                    plan.popular ? 'ring-4 ring-opacity-50' : ''
                  }`}
                  style={{
                    background: plan.popular 
                      ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 230, 210, 0.8))'
                      : 'linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(240, 230, 210, 0.6))',
                    border: plan.popular 
                      ? '3px solid #E6B87A'
                      : '2px solid #D4C4A8',
                    boxShadow: plan.popular 
                      ? '0 20px 40px rgba(230, 184, 122, 0.3)'
                      : '0 10px 30px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  {plan.popular && (
                    <div 
                      className="absolute -top-4 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded-full text-white font-bold text-sm"
                      style={{
                        background: 'linear-gradient(135deg, #E6B87A, #D4A574)',
                        boxShadow: '0 4px 15px rgba(230, 184, 122, 0.4)'
                      }}
                    >
                      最受欢迎
                    </div>
                  )}
                  
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold mb-2" style={{ color: '#5D4037' }}>
                      {plan.name}
                    </h3>
                    <p className="text-lg opacity-80" style={{ color: '#8B4513' }}>
                      {plan.description}
                    </p>
                  </div>
                  
                  <div className="text-center mb-6">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <span className="text-4xl font-bold" style={{ color: plan.color }}>
                        {plan.price}
                      </span>
                      <span 
                        className="text-lg line-through opacity-60"
                        style={{ color: '#8B4513' }}
                      >
                        {plan.originalPrice}
                      </span>
                    </div>
                    <p className="text-lg font-semibold" style={{ color: '#5D4037' }}>
                      {plan.requests} AI分析
                    </p>
                  </div>
                  
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, featureIdx) => (
                      <li key={featureIdx} className="flex items-center gap-3 text-base">
                        <span 
                          className="w-2 h-2 rounded-full flex-shrink-0"
                          style={{ background: plan.color }}
                        ></span>
                        <span style={{ color: '#5D4037' }}>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <button
                    className="w-full py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105"
                    style={{
                      background: `linear-gradient(135deg, ${plan.color}, ${plan.color}dd)`,
                      color: '#FEFCF5',
                      boxShadow: `0 8px 25px ${plan.color}40`
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.transform = 'scale(1.05)';
                      e.target.style.boxShadow = `0 12px 35px ${plan.color}60`;
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.transform = 'scale(1)';
                      e.target.style.boxShadow = `0 8px 25px ${plan.color}40`;
                    }}
                  >
                    立即购买
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact */}
        <section>
          <div 
            className="rounded-3xl p-12 text-center relative overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 230, 210, 0.8))',
              border: '2px solid #E6D7B8',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* 装饰性背景元素 */}
            <div className="absolute top-0 right-0 w-40 h-40 opacity-5">
              <div className="w-full h-full rounded-full" style={{
                background: 'linear-gradient(135deg, #D4A574, #E6B87A)'
              }}></div>
            </div>
            
            <h2 
              className="text-4xl font-bold mb-6"
              style={{ 
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                background: 'linear-gradient(135deg, #D4A574, #E6B87A)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              📞 联系我们
            </h2>
            <p className="text-xl mb-10 opacity-90" style={{ color: '#8B4513' }}>
              有任何问题或建议？我们随时为你提供帮助！
            </p>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                { icon: '📧', title: '邮箱支持', content: '<EMAIL>' },
                { icon: '💬', title: '在线客服', content: '工作日 9:00-18:00' },
                { icon: '📱', title: '微信客服', content: 'duibai-ai-support' }
              ].map((contact, idx) => (
                <div 
                  key={idx}
                  className="p-8 rounded-2xl transition-all duration-300 hover:scale-105"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(240, 230, 210, 0.6))',
                    border: '2px solid #D4C4A8',
                    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  <div className="text-4xl mb-4">{contact.icon}</div>
                  <h3 className="text-2xl font-bold mb-3" style={{ color: '#5D4037' }}>{contact.title}</h3>
                  <p className="text-lg" style={{ color: '#8B4513' }}>{contact.content}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer 
        className="mt-20 py-12 text-center"
        style={{
          background: 'linear-gradient(135deg, rgba(240, 230, 210, 0.8), rgba(230, 215, 184, 0.6))',
          borderTop: '2px solid #E6D7B8'
        }}
      >
        <div className="max-w-7xl mx-auto px-6">
          <h3 
            className="text-3xl font-bold mb-4 flex items-center justify-center gap-3"
            style={{ 
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              background: 'linear-gradient(135deg, #8B4513, #D2691E)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}
          >
            <span className="text-4xl">⛅</span>
            對白 AI
          </h3>
          <p className="text-xl mb-4" style={{ color: '#8B4513' }}>让英语学习更智能、更高效</p>
          <p className="text-sm opacity-70" style={{ color: '#5D4037' }}>&copy; 2024 對白 AI. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default HelpPage;
