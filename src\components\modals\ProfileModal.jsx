import { ArrowLeft, FileText } from 'lucide-react';
import React, { useState } from 'react';
import AvatarUpload from '../AvatarUpload';
import DiarySection from '../DiarySection';
import RecentImagesGrid from '../RecentImagesGrid';

const ProfileModal = ({ isOpen, onClose, profileType, isDarkMode, onShareWriting, autoPlayTTS, onChatWithDiary, onAvatarChange }) => {
  const [showDiary, setShowDiary] = useState(profileType === 'alex');
  const [userAvatar, setUserAvatar] = useState(null);
  // const generateDiaryFnRef = useRef(null); // 移除 - 不再需要手动生成日记

  // 加载保存的头像
  React.useEffect(() => {
    try {
      const saved = localStorage.getItem('user_avatar');
      if (saved) {
        const avatarData = JSON.parse(saved);
        setUserAvatar(avatarData.image);
      }
    } catch (e) {
      console.error('Failed to load avatar:', e);
    }
  }, []);

  const handleAvatarChange = (newAvatar) => {
    setUserAvatar(newAvatar);
    if (onAvatarChange) {
      onAvatarChange(newAvatar);
    }
  };

  if (!isOpen) return null;

  const isAlex = profileType === 'alex';

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ backgroundColor: 'var(--modal-backdrop)' }}
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div
        className="w-full max-w-md rounded-2xl shadow-2xl overflow-hidden"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
        }}
      >
        {!showDiary && (
          <>
            <div
              className="relative h-32"
              style={{
                background: isAlex
                  ? 'linear-gradient(135deg, #2D5A2D, #4A7C59, #6B8E6B)'
                  : 'linear-gradient(135deg, #8B4513, #D2691E, #CD853F)'
              }}
            >
              <div className="absolute inset-0 opacity-20">
                {isAlex ? (
                  <div className="flex items-center justify-center h-full text-6xl">🌿🌺🍃</div>
                ) : (
                  <div className="flex items-center justify-center h-full text-6xl">✨📝💭</div>
                )}
              </div>
            </div>

            <div className="relative -mt-12 flex justify-center">
              {isAlex ? (
                <div
                  className="w-20 h-20 rounded-full flex items-center justify-center border-4"
                  style={{
                    backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                    borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <span style={{ fontSize: '32px' }}>🌿</span>
                </div>
              ) : (
                <AvatarUpload
                  currentAvatar={userAvatar}
                  onAvatarChange={handleAvatarChange}
                  isDarkMode={isDarkMode}
                  size="large"
                  showUploadButton={false}
                />
              )}
            </div>
          </>
        )}

        {showDiary && isAlex && (
          <div
            className="flex items-center justify-between p-4 border-b"
            style={{
              backgroundColor: isDarkMode ? '#332B22' : '#F9F7F4',
              borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8'
            }}
          >
            <button
              onClick={() => setShowDiary(false)}
              className="p-2 rounded-lg transition-colors duration-200"
              style={{
                color: isDarkMode ? '#C4B59A' : '#8B4513',
                backgroundColor: 'transparent'
              }}
              title="返回个人信息"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h2
              className="text-lg font-bold"
              style={{
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}
            >
              Alex's Daily Journal
            </h2>
            {/* 移除手动生成日记按钮 - 现在通过 Netlify 函数自动生成 */}
          </div>
        )}

        {!showDiary && (
          <div className="px-6 pt-2 pb-4">
            <div className="text-center mb-4">
              <h2
                className="text-2xl font-bold mb-2"
                style={{
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                {isAlex ? 'Alex' : '用户'}
              </h2>
              <p
                className="text-sm mb-4"
                style={{
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                {isAlex
                  ? '植物学家 & 自然摄影师 🌿📸'
                  : '英语学习者 📚✨'
                }
              </p>
            </div>
          </div>
        )}

        <div className={showDiary ? "" : "px-6 pb-6"}>
          {isAlex ? (
            showDiary ? (
              <div className="overflow-y-auto custom-scrollbar" style={{ maxHeight: 'calc(80vh - 80px)' }}>
                <div className="p-4">
                  <DiarySection
                    isDarkMode={isDarkMode}
                    autoPlayTTS={autoPlayTTS}
                    isInModal={true}
                    isCompact={false}
                    // onGenerateDiary 不再需要 - 移除手动生成功能
                    onChatWithDiary={onChatWithDiary}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* AI生成图像缩略图网格 */}
                <RecentImagesGrid isDarkMode={isDarkMode} onImageClick={(diaryId) => {
                  // 点击图片跳转到日记页面，滚动到最后浏览位置
                  setShowDiary(true);
                }} />
              </div>
            )
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>🎯</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  提高英语表达能力
                </div>
              </div>

              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>💬</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  与AI进行英语对话练习
                </div>
              </div>

              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>📚</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  使用词典查询功能学习新词汇
                </div>
              </div>

              <div className="flex items-center gap-3 py-2">
                <span style={{ fontSize: '18px' }}>📝</span>
                <div
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  分享你的写作作品获得反馈
                </div>
              </div>

              <button
                onClick={() => {
                  onClose();
                  onShareWriting();
                }}
                className="w-full mt-4 py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center gap-2"
                style={{
                  backgroundColor: isDarkMode ? '#D2691E' : '#166534',
                  color: '#FEFCF5',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                <FileText className="w-4 h-4" />
                分享写作历史
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileModal;
