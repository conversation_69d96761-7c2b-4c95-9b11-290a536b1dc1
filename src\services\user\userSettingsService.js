import { deleteDoc, doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../config/firebaseConfig';

/**
 * 用户设置服务
 * 管理用户在Firebase Firestore中的设置数据和系统配置
 */

/**
 * 获取系统配置（包括共享的API Key）
 * @returns {Promise<Object>} 系统配置对象
 */
export const getSystemConfig = async () => {
  try {
    console.log('🔧 正在获取系统配置...');
    const configDocRef = doc(db, 'systemConfig', 'main');
    const configDoc = await getDoc(configDocRef);

    if (configDoc.exists()) {
      const config = configDoc.data();
      console.log('✅ 成功获取系统配置:', Object.keys(config));
      return config;
    } else {
      console.log('📝 系统配置不存在，创建默认配置...');
      // 如果系统配置不存在，返回默认配置
      const defaultConfig = {
        doubaoApiKey: '5f480627-1927-49b3-8dc4-0e3f47a75a99', // 您提供的API Key
        maxRequestsPerDay: 100, // 每日请求限制
        enablePaymentSystem: false, // 支付系统开关
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 创建默认系统配置
      await setDoc(configDocRef, defaultConfig);
      console.log('✅ 默认系统配置创建成功');
      return defaultConfig;
    }
  } catch (error) {
    console.error('❌ 获取系统配置失败:', error);
    console.log('🔄 返回默认配置');
    // 如果Firebase连接失败，返回默认配置而不是抛出错误
    return {
      doubaoApiKey: '5f480627-1927-49b3-8dc4-0e3f47a75a99',
      maxRequestsPerDay: 100,
      enablePaymentSystem: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }
};

/**
 * 获取豆包API Key（从系统配置中）
 * @returns {Promise<string>} 豆包API Key
 */
export const getDoubaoApiKey = async () => {
  try {
    console.log('🔑 正在获取豆包API Key...');
    const config = await getSystemConfig();
    const apiKey = config.doubaoApiKey || '';

    if (apiKey) {
      console.log('✅ 成功获取API Key:', apiKey.substring(0, 8) + '...');
      return apiKey;
    } else {
      console.warn('⚠️ 系统配置中没有找到API Key，使用备用Key');
      return '5f480627-1927-49b3-8dc4-0e3f47a75a99';
    }
  } catch (error) {
    console.error('❌ 获取豆包API Key失败:', error);
    console.log('🔄 使用备用API Key');
    // 如果Firebase获取失败，返回备用的API Key
    return '5f480627-1927-49b3-8dc4-0e3f47a75a99';
  }
};

/**
 * 获取用户设置
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 用户设置对象
 */
export const getUserSettings = async (userId) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    const userDocRef = doc(db, 'userSettings', userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      return userDoc.data();
    } else {
      // 如果用户设置不存在，返回默认设置
      const defaultSettings = {
        theme: 'light',
        autoPlayTTS: false,
        aiResponseSound: true,
        autoShowTranslation: true,
        autoShowSuggestion: false,
        requestsUsedToday: 0,
        lastRequestDate: new Date().toISOString().split('T')[0],
        isPremiumUser: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 创建默认设置文档
      await setDoc(userDocRef, defaultSettings);
      return defaultSettings;
    }
  } catch (error) {
    console.error('获取用户设置失败:', error);
    throw new Error('获取用户设置失败: ' + error.message);
  }
};

/**
 * 更新用户设置
 * @param {string} userId - 用户ID
 * @param {Object} settings - 要更新的设置
 * @returns {Promise<void>}
 */
export const updateUserSettings = async (userId, settings) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    const userDocRef = doc(db, 'userSettings', userId);
    const updateData = {
      ...settings,
      updatedAt: new Date().toISOString()
    };

    await updateDoc(userDocRef, updateData);
  } catch (error) {
    console.error('更新用户设置失败:', error);
    throw new Error('更新用户设置失败: ' + error.message);
  }
};

/**
 * 检查用户是否可以使用API（基于使用量限制）
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 包含是否可用和剩余次数的对象
 */
export const checkApiUsageLimit = async (userId) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    const settings = await getUserSettings(userId);
    const systemConfig = await getSystemConfig();
    const today = new Date().toISOString().split('T')[0];

    // 如果是新的一天，重置使用次数
    let requestsUsedToday = settings.requestsUsedToday || 0;
    if (settings.lastRequestDate !== today) {
      requestsUsedToday = 0;
    }

    // 确保maxRequestsPerDay有默认值
    const maxRequestsPerDay = systemConfig.maxRequestsPerDay || 100;
    const maxRequests = settings.isPremiumUser ? 1000 : maxRequestsPerDay;
    const remainingRequests = maxRequests - requestsUsedToday;

    console.log('使用量检查:', {
      userId,
      requestsUsedToday,
      maxRequests,
      remainingRequests,
      isPremiumUser: settings.isPremiumUser,
      systemConfig: systemConfig
    });

    return {
      canUse: remainingRequests > 0,
      remainingRequests,
      maxRequests,
      isPremiumUser: settings.isPremiumUser
    };
  } catch (error) {
    console.error('检查API使用限制失败:', error);
    throw new Error('检查API使用限制失败: ' + error.message);
  }
};

/**
 * 记录API使用（增加使用次数）
 * @param {string} userId - 用户ID
 * @returns {Promise<void>}
 */
export const recordApiUsage = async (userId) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    const settings = await getUserSettings(userId);
    const today = new Date().toISOString().split('T')[0];

    // 如果是新的一天，重置使用次数
    let requestsUsedToday = settings.requestsUsedToday || 0;
    if (settings.lastRequestDate !== today) {
      requestsUsedToday = 0;
    }

    await updateUserSettings(userId, {
      requestsUsedToday: requestsUsedToday + 1,
      lastRequestDate: today
    });
  } catch (error) {
    console.error('记录API使用失败:', error);
    throw new Error('记录API使用失败: ' + error.message);
  }
};

/**
 * 保存用户主题设置
 * @param {string} userId - 用户ID
 * @param {string} theme - 主题 ('light' 或 'dark')
 * @returns {Promise<void>}
 */
export const saveThemeSettings = async (userId, theme) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    await updateUserSettings(userId, { theme });
  } catch (error) {
    console.error('保存主题设置失败:', error);
    throw new Error('保存主题设置失败: ' + error.message);
  }
};

/**
 * 保存语音设置
 * @param {string} userId - 用户ID
 * @param {boolean} autoPlayTTS - 自动播放TTS
 * @param {boolean} aiResponseSound - AI回复音效
 * @returns {Promise<void>}
 */
export const saveVoiceSettings = async (userId, autoPlayTTS, aiResponseSound) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    await updateUserSettings(userId, {
      autoPlayTTS,
      aiResponseSound
    });
  } catch (error) {
    console.error('保存语音设置失败:', error);
    throw new Error('保存语音设置失败: ' + error.message);
  }
};

/**
 * 保存聊天设置
 * @param {string} userId - 用户ID
 * @param {boolean} autoShowTranslation - 是否自动显示翻译
 * @param {boolean} autoShowSuggestion - 是否自动显示纠错建议
 * @returns {Promise<void>}
 */
export const saveChatSettings = async (userId, autoShowTranslation, autoShowSuggestion) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    await updateUserSettings(userId, {
      autoShowTranslation,
      autoShowSuggestion
    });
  } catch (error) {
    console.error('保存聊天设置失败:', error);
    throw new Error('保存聊天设置失败: ' + error.message);
  }
};

/**
 * 初始化用户设置（用于新用户注册后）
 * @param {string} userId - 用户ID
 * @param {string} email - 用户邮箱
 * @returns {Promise<void>}
 */
export const initializeUserSettings = async (userId, email) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    const userDocRef = doc(db, 'userSettings', userId);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      const defaultSettings = {
        email,
        theme: 'light',
        autoPlayTTS: false,
        aiResponseSound: true,
        requestsUsedToday: 0,
        lastRequestDate: new Date().toISOString().split('T')[0],
        isPremiumUser: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await setDoc(userDocRef, defaultSettings);
    }
  } catch (error) {
    console.error('初始化用户设置失败:', error);
    throw new Error('初始化用户设置失败: ' + error.message);
  }
};

/**
 * 升级用户为高级用户
 * @param {string} userId - 用户ID
 * @returns {Promise<void>}
 */
export const upgradeUserToPremium = async (userId) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    await updateUserSettings(userId, {
      isPremiumUser: true,
      upgradeDate: new Date().toISOString()
    });
  } catch (error) {
    console.error('升级用户失败:', error);
    throw new Error('升级用户失败: ' + error.message);
  }
};

/**
 * 删除用户设置（用于用户删除账户时）
 * @param {string} userId - 用户ID
 * @returns {Promise<void>}
 */
export const deleteUserSettings = async (userId) => {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    const userDocRef = doc(db, 'userSettings', userId);
    await deleteDoc(userDocRef);
  } catch (error) {
    console.error('删除用户设置失败:', error);
    throw new Error('删除用户设置失败: ' + error.message);
  }
};
