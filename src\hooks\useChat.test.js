import { act, renderHook, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { parseAIResponse, useChat } from './useChat';

// Mock dependencies
vi.mock('../services/ai/ttsService', () => ({
  ttsService: {
    speak: vi.fn().mockResolvedValue(),
    isSupported: vi.fn().mockReturnValue(true),
    addStatusListener: vi.fn(),
    removeStatusListener: vi.fn()
  }
}));

vi.mock('../services/chat/chatResponseService', () => ({
  getChatResponse: vi.fn()
}));

vi.mock('../services/chat/greetingService', () => ({
  generateNewGreeting: vi.fn()
}));

vi.mock('../services/storage/simpleStorageService', () => ({
  default: {
    saveChatSession: vi.fn()
  }
}));

vi.mock('../utils/idGenerator', () => ({
  generateUniqueId: vi.fn()
}));

vi.mock('../utils/soundUtils', () => ({
  playAIResponseSound: vi.fn()
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
global.localStorage = localStorageMock;

describe('parseAIResponse', () => {
  it('应该正确解析包含分隔符的响应', () => {
    const response = 'Hello there!---你好！';
    const result = parseAIResponse(response);

    expect(result).toEqual({
      english: 'Hello there!',
      chinese: '你好！'
    });
  });

  it('应该处理没有分隔符的响应', () => {
    const response = 'Hello there!';
    const result = parseAIResponse(response);

    expect(result).toEqual({
      english: 'Hello there!',
      chinese: '翻译暂不可用'
    });
  });

  it('应该处理非字符串输入', () => {
    const response = { message: 'Hello' };
    const result = parseAIResponse(response);

    expect(result).toEqual({
      english: '[object Object]',
      chinese: '翻译暂不可用'
    });
  });

  it('应该正确处理多个分隔符', () => {
    const response = 'Hello there!---你好！---额外内容';
    const result = parseAIResponse(response);

    expect(result).toEqual({
      english: 'Hello there!',
      chinese: '你好！'
    });
  });
});

describe('useChat', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);

    const { generateUniqueId } = await import('../utils/idGenerator');
    vi.mocked(generateUniqueId).mockReturnValue('test-id-123');
  });

  describe('初始化', () => {
    it('应该使用默认初始消息', () => {
      const { result } = renderHook(() => useChat(false, null, false, true));

      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0].type).toBe('ai');
      expect(result.current.messages[0].content).toContain('Hey there!');
    });

    it('应该从localStorage恢复消息', () => {
      const savedMessages = [
        { id: 1, type: 'ai', content: 'Hello', timestamp: '2024-01-01T00:00:00.000Z' },
        { id: 2, type: 'user', content: 'Hi', timestamp: '2024-01-01T00:01:00.000Z' }
      ];
      localStorageMock.getItem.mockReturnValue(JSON.stringify(savedMessages));

      const { result } = renderHook(() => useChat(false, null, false, true));

      expect(result.current.messages).toHaveLength(2);
      expect(result.current.messages[0].content).toBe('Hello');
      expect(result.current.messages[1].content).toBe('Hi');
    });

    it('应该处理localStorage中的无效数据', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');

      const { result } = renderHook(() => useChat(false, null, false, true));

      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0].content).toContain('Hey there!');
    });
  });

  describe('发送消息', () => {
    it('应该能够发送文本消息', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');

      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      expect(result.current.messages).toHaveLength(3); // 初始消息 + 用户消息 + AI回复
      expect(result.current.messages[1].type).toBe('user');
      expect(result.current.messages[1].content).toBe('Hello');
      expect(result.current.messages[2].type).toBe('ai');
      expect(result.current.messages[2].content).toBe('AI response');
      expect(result.current.messages[2].translation).toBe('AI回复');
    });

    it('应该能够发送带图片的消息', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');
      const images = [{ url: 'test.jpg', type: 'image/jpeg' }];

      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleSendMessage('Look at this image', images);
      });

      expect(result.current.messages[1].images).toEqual(images);
      expect(vi.mocked(getChatResponse)).toHaveBeenCalledWith(
        'Look at this image',
        expect.any(Array),
        null,
        true,
        images
      );
    });

    it('应该在autoShowTranslation为false时不显示翻译', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');

      const { result } = renderHook(() => useChat(false, null, false, false));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      expect(result.current.messages[2].translation).toBeNull();
    });

    it('应该处理空消息', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleSendMessage('   ');
      });

      expect(result.current.messages).toHaveLength(1); // 只有初始消息
      expect(vi.mocked(getChatResponse)).not.toHaveBeenCalled();
    });

    it('应该在加载时阻止重复发送', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      vi.mocked(getChatResponse).mockImplementation(() => new Promise(() => {})); // 永不resolve

      const { result } = renderHook(() => useChat(false, null, false, true));

      act(() => {
        result.current.handleSendMessage('First message');
      });

      // 确保第一个消息开始处理后再发送第二个
      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.handleSendMessage('Second message');
      });

      expect(vi.mocked(getChatResponse)).toHaveBeenCalledTimes(1);
    });

    it('应该处理API错误', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      vi.mocked(getChatResponse).mockRejectedValue(new Error('API Error'));

      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      expect(result.current.messages).toHaveLength(3);
      expect(result.current.messages[2].content).toContain('having trouble connecting');
    });

    it('应该在autoPlayTTS为true时播放TTS', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      const { ttsService } = await import('../services/ai/ttsService');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');

      const { result } = renderHook(() => useChat(true, null, false, true));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      await waitFor(() => {
        expect(vi.mocked(ttsService.speak)).toHaveBeenCalledWith(
          'AI response',
          { lang: 'en-US', rate: 0.9, pitch: 1.0, volume: 1.0 }
        );
      });
    });

    it('应该在aiResponseSound为true时播放响应音效', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      const { playAIResponseSound } = await import('../utils/soundUtils');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');

      const { result } = renderHook(() => useChat(false, null, true, true));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      await waitFor(() => {
        expect(vi.mocked(playAIResponseSound)).toHaveBeenCalledWith(true);
      });
    });

    it('应该保存聊天会话', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      const simpleStorageService = await import('../services/storage/simpleStorageService');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');

      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      await waitFor(() => {
        expect(vi.mocked(simpleStorageService.default.saveChatSession)).toHaveBeenCalled();
      });
    });
  });

  describe('新对话', () => {
    it('应该能够开始新对话', async () => {
      const { generateNewGreeting } = await import('../services/chat/greetingService');
      vi.mocked(generateNewGreeting).mockResolvedValue('New greeting---新问候');

      const { result } = renderHook(() => useChat(false, null, false, true));

      // 先发送一条消息
      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      // 然后开始新对话
      await act(async () => {
        await result.current.handleNewConversation();
      });

      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0].content).toBe('New greeting');
      expect(result.current.messages[0].translation).toBe('新问候');
    });

    it('应该在生成新问候失败时使用默认消息', async () => {
      const { generateNewGreeting } = await import('../services/chat/greetingService');
      vi.mocked(generateNewGreeting).mockRejectedValue(new Error('Greeting error'));

      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleNewConversation();
      });

      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0].content).toContain('Hey there!');
    });

    it('应该在新对话时清除localStorage', async () => {
      const { generateNewGreeting } = await import('../services/chat/greetingService');
      vi.mocked(generateNewGreeting).mockResolvedValue('New greeting---新问候');

      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleNewConversation();
      });

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('current_chat_messages');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('current_chat_suggestions');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('shared_writing_context');
    });

    it('应该在加载时阻止新对话', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      const { generateNewGreeting } = await import('../services/chat/greetingService');
      vi.mocked(getChatResponse).mockImplementation(() => new Promise(() => {})); // 永不resolve

      const { result } = renderHook(() => useChat(false, null, false, true));

      act(() => {
        result.current.handleSendMessage('Hello');
      });

      // 确保处于加载状态
      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.handleNewConversation();
      });

      expect(vi.mocked(generateNewGreeting)).not.toHaveBeenCalled();
    });
  });

  describe('状态管理', () => {
    it('应该正确管理加载状态', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      let resolvePromise;
      vi.mocked(getChatResponse).mockImplementation(() => new Promise(resolve => {
        resolvePromise = resolve;
      }));

      const { result } = renderHook(() => useChat(false, null, false, true));

      expect(result.current.isLoading).toBe(false);

      act(() => {
        result.current.handleSendMessage('Hello');
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolvePromise('AI response---AI回复');
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('应该提供messagesEndRef', () => {
      const { result } = renderHook(() => useChat(false, null, false, true));

      expect(result.current.messagesEndRef).toBeDefined();
      expect(result.current.messagesEndRef.current).toBeNull();
    });
  });

  describe('localStorage同步', () => {
    it('应该在消息更新时保存到localStorage', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');

      const { result } = renderHook(() => useChat(false, null, false, true));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'current_chat_messages',
        expect.stringContaining('Hello')
      );
    });
  });

  describe('共享写作上下文', () => {
    it('应该将共享写作上下文传递给getChatResponse', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      vi.mocked(getChatResponse).mockResolvedValue('AI response---AI回复');
      const sharedContext = { title: 'Test Article', content: 'Test content' };

      const { result } = renderHook(() => useChat(false, sharedContext, false, true));

      await act(async () => {
        await result.current.handleSendMessage('Hello');
      });

      expect(vi.mocked(getChatResponse)).toHaveBeenCalledWith(
        'Hello',
        expect.any(Array),
        sharedContext,
        true,
        []
      );
    });
  });
});
