import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { vi } from 'vitest';
import { AppProvider } from '../context/AppContext';
import VintageApiConfigModal from './VintageApiConfigModal';

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  X: () => <div data-testid="x-icon">X</div>,
  Key: () => <div data-testid="key-icon">Key</div>,
  ExternalLink: () => <div data-testid="external-link-icon">ExternalLink</div>,
  AlertCircle: () => <div data-testid="alert-circle-icon">AlertCircle</div>,
  CheckCircle: () => <div data-testid="check-circle-icon">CheckCircle</div>,
  Sun: () => <div data-testid="sun-icon">Sun</div>,
  Moon: () => <div data-testid="moon-icon">Moon</div>,
  Volume2: () => <div data-testid="volume2-icon">Volume2</div>,
  MessageCircle: () => <div data-testid="message-circle-icon">MessageCircle</div>,
  LogOut: () => <div data-testid="logout-icon">LogOut</div>,
  User: () => <div data-testid="user-icon">User</div>,
  Zap: () => <div data-testid="zap-icon">Zap</div>,
  Crown: () => <div data-testid="crown-icon">Crown</div>,
  RefreshCw: () => <div data-testid="refresh-cw-icon">RefreshCw</div>,
  Wifi: () => <div data-testid="wifi-icon">Wifi</div>,
  WifiOff: () => <div data-testid="wifi-off-icon">WifiOff</div>,
  BookOpen: () => <div data-testid="book-open-icon">BookOpen</div>,
  Globe: () => <div data-testid="globe-icon">Globe</div>,
  Database: () => <div data-testid="database-icon">Database</div>,
}));

// Mock userSettingsService
vi.mock('../services/user/userSettingsService', () => ({
  getUserSettings: vi.fn(),
  saveThemeSettings: vi.fn(() => Promise.resolve()),
  saveVoiceSettings: vi.fn(() => Promise.resolve()),
  saveChatSettings: vi.fn(() => Promise.resolve()),
  initializeUserSettings: vi.fn(),
  checkApiUsageLimit: vi.fn(),
}));


// 测试包装器组件
const TestWrapper = ({ children }) => {
  return <AppProvider>{children}</AppProvider>;
};

describe('VintageApiConfigModal', () => {
  const defaultProps = {
    isOpen: false,
    onClose: vi.fn(),
    onSave: vi.fn(),
    isDarkMode: false,
    onToggleDarkMode: vi.fn(),
    autoPlayTTS: false,
    onToggleAutoPlayTTS: vi.fn(),
    aiResponseSound: false,
    onToggleAIResponseSound: vi.fn(),
    dictionaryService: 'free_dictionary',
    onDictionaryServiceChange: vi.fn(),
    autoShowTranslation: false,
    onToggleAutoShowTranslation: vi.fn(),
    autoShowSuggestion: false,
    onToggleAutoShowSuggestion: vi.fn(),
    user: null,
    onLogout: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('does not render when isOpen is false', () => {
    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={false} />
      </TestWrapper>
    );
    expect(screen.queryByText('设置')).not.toBeInTheDocument();
  });

  it('renders when isOpen is true', () => {
    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={true} />
      </TestWrapper>
    );
    expect(screen.getByText('设置')).toBeInTheDocument();
    expect(screen.getByTestId('x-icon')).toBeInTheDocument();
  });

  it('loads user settings and usage info when user is logged in and modal opens', async () => {
    const { getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
    getUserSettings.mockResolvedValue({});
    checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(getUserSettings).toHaveBeenCalledWith('test-user');
      expect(checkApiUsageLimit).toHaveBeenCalledWith('test-user');
      expect(screen.getByText('50/100')).toBeInTheDocument();
    });
  });

  it('displays error message if loading user settings fails', async () => {
    const { getUserSettings } = await import('../services/user/userSettingsService');
    getUserSettings.mockRejectedValue(new Error('Failed to load settings'));

    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('加载设置失败，请重试')).toBeInTheDocument();
    });
  });

  it('toggles dark mode', async () => {
    const { getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
    getUserSettings.mockResolvedValue({});
    checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

    await act(async () => {
      render(
        <TestWrapper>
          <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} isDarkMode={false} />
        </TestWrapper>
      );
    });

    // 等待组件加载完成
    await waitFor(() => {
      expect(getUserSettings).toHaveBeenCalledWith('test-user');
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('theme-toggle-btn'));
    });

    expect(defaultProps.onToggleDarkMode).toHaveBeenCalledTimes(1);
  });

  it('toggles autoPlayTTS', async () => {
    const { getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
    getUserSettings.mockResolvedValue({});
    checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

    await act(async () => {
      render(
        <TestWrapper>
          <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} autoPlayTTS={false} />
        </TestWrapper>
      );
    });

    // 等待组件加载完成
    await waitFor(() => {
      expect(getUserSettings).toHaveBeenCalledWith('test-user');
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('autoplay-tts-toggle-btn'));
    });

    expect(defaultProps.onToggleAutoPlayTTS).toHaveBeenCalledTimes(1);
  });

  it('toggles aiResponseSound', async () => {
    const { getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
    getUserSettings.mockResolvedValue({});
    checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

    await act(async () => {
      render(
        <TestWrapper>
          <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} aiResponseSound={false} />
        </TestWrapper>
      );
    });

    // 等待组件加载完成
    await waitFor(() => {
      expect(getUserSettings).toHaveBeenCalledWith('test-user');
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('ai-response-sound-toggle-btn'));
    });

    expect(defaultProps.onToggleAIResponseSound).toHaveBeenCalledTimes(1);
  });

  it('changes dictionary service', () => {
    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={true} dictionaryService="free_dictionary" />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('英汉字典'));

    expect(defaultProps.onDictionaryServiceChange).toHaveBeenCalledWith('ecdict');
  });

  it('displays user email and calls onLogout', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user', email: '<EMAIL>' }} />
        </TestWrapper>
      );
    });

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    await act(async () => {
      fireEvent.click(screen.getByText('退出登录'));
    });

    expect(defaultProps.onLogout).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('displays unauthenticated user message when no user is logged in', () => {
    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={true} user={null} />
      </TestWrapper>
    );
    expect(screen.getByText('请先登录以使用 AI 分析功能和云端设置同步')).toBeInTheDocument();
  });

  it('calls onClose when X button is clicked', () => {
    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={true} />
      </TestWrapper>
    );
    fireEvent.click(screen.getByTestId('x-icon'));
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when backdrop is clicked', () => {
    render(
      <TestWrapper>
        <VintageApiConfigModal {...defaultProps} isOpen={true} />
      </TestWrapper>
    );
    fireEvent.click(screen.getByTestId('modal-backdrop')); // The backdrop
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  // 测试新的四个按钮功能
  describe('New Settings Buttons', () => {
    it('toggles auto show translation and saves settings', async () => {
      const { saveChatSettings, getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
      getUserSettings.mockResolvedValue({});
      checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

      await act(async () => {
        render(
          <TestWrapper>
            <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} autoShowTranslation={false} />
          </TestWrapper>
        );
      });

      await act(async () => {
        fireEvent.click(screen.getByTestId('auto-show-translation-toggle-btn'));
      });

      expect(defaultProps.onToggleAutoShowTranslation).toHaveBeenCalledTimes(1);
      await waitFor(() => {
        expect(saveChatSettings).toHaveBeenCalledWith('test-user', true, false);
      });
    });

    it('toggles auto show suggestion and saves settings', async () => {
      const { saveChatSettings, getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
      getUserSettings.mockResolvedValue({});
      checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

      await act(async () => {
        render(
          <TestWrapper>
            <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} autoShowSuggestion={false} />
          </TestWrapper>
        );
      });

      await act(async () => {
        fireEvent.click(screen.getByTestId('auto-show-suggestion-toggle-btn'));
      });

      expect(defaultProps.onToggleAutoShowSuggestion).toHaveBeenCalledTimes(1);
      await waitFor(() => {
        expect(saveChatSettings).toHaveBeenCalledWith('test-user', false, true);
      });
    });

    it('displays correct text for auto show translation', async () => {
      const { getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
      getUserSettings.mockResolvedValue({});
      checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

      render(
        <TestWrapper>
          <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} autoShowTranslation={true} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('自动翻译')).toBeInTheDocument();
      });
    });

    it('displays correct text for auto show suggestion', async () => {
      const { getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
      getUserSettings.mockResolvedValue({});
      checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

      render(
        <TestWrapper>
          <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} autoShowSuggestion={true} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('自动纠错')).toBeInTheDocument();
      });
    });

    it('shows correct icons for the new settings', () => {
      render(
        <TestWrapper>
          <VintageApiConfigModal {...defaultProps} isOpen={true} />
        </TestWrapper>
      );
      expect(screen.getByTestId('globe-icon')).toBeInTheDocument(); // 自动显示翻译图标
      expect(screen.getAllByTestId('message-circle-icon')).toHaveLength(2); // 有两个MessageCircle图标：AI回复音效和自动显示纠错
    });

    it('handles both new settings toggles together', async () => {
      const { saveChatSettings, getUserSettings, checkApiUsageLimit } = await import('../services/user/userSettingsService');
      getUserSettings.mockResolvedValue({});
      checkApiUsageLimit.mockResolvedValue({ canUse: true, maxRequests: 100, remainingRequests: 50 });

      await act(async () => {
        render(
          <TestWrapper>
            <VintageApiConfigModal {...defaultProps} isOpen={true} user={{ uid: 'test-user' }} autoShowTranslation={false} autoShowSuggestion={false} />
          </TestWrapper>
        );
      });

      // 测试翻译开关
      await act(async () => {
        fireEvent.click(screen.getByTestId('auto-show-translation-toggle-btn'));
      });
      expect(defaultProps.onToggleAutoShowTranslation).toHaveBeenCalledTimes(1);

      // 测试纠错开关
      await act(async () => {
        fireEvent.click(screen.getByTestId('auto-show-suggestion-toggle-btn'));
      });
      expect(defaultProps.onToggleAutoShowSuggestion).toHaveBeenCalledTimes(1);

      // 验证两个设置都被保存
      await waitFor(() => {
        expect(saveChatSettings).toHaveBeenCalledTimes(2);
      });
    });
  });
});
