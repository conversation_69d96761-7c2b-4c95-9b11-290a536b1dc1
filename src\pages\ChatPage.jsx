import { Book<PERSON><PERSON>, FileText, History, Send, Settings, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import AuthModal from '../components/auth/AuthModal';
import ChatEdgeNavigationArrow from '../components/ChatEdgeNavigationArrow';
import ChatHistoryModal from '../components/ChatHistoryModal';
import ProfileModal from '../components/modals/ProfileModal';
import WritingHistoryModal from '../components/modals/WritingHistoryModal';
import VoiceInputButton from '../components/VoiceInputButton';
import VoicePlayButton from '../components/VoicePlayButton';

import { useAppContext } from '../context/AppContext';
import { parseAIResponse, useChat } from '../hooks/useChat';
import { getExpressionSuggestion } from '../services/ai/expressionService';
import { onAuthChange } from '../services/auth/authService';
import { translateMessage } from '../services/chat/chatResponseService';
import { generateWritingSharingResponse } from '../services/writing/writingSharingService';
import { generateUniqueId } from '../utils/idGenerator';

const EnglishChatPage = ({ onBackToEditor, onShowApiConfig, isDarkMode, autoPlayTTS, aiResponseSound, autoShowTranslation, autoShowSuggestion }) => {
  const { dispatch } = useAppContext();

  const [user, setUser] = useState(null);
  const [showAuthModal, setShowAuthModal] = useState(false);

  const [sharedWritingContext, setSharedWritingContext] = useState(() => {
    try {
      const saved = localStorage.getItem('shared_writing_context');
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      return null;
    }
  });

  const { messages, setMessages, isLoading, handleSendMessage, handleNewConversation, messagesEndRef } = useChat(autoPlayTTS, sharedWritingContext, aiResponseSound, autoShowTranslation);

  const [inputText, setInputText] = useState('');
  const [hoveredMessage, setHoveredMessage] = useState(null);
  const [showChatHistory, setShowChatHistory] = useState(false);
  const [showFloatingHeader, setShowFloatingHeader] = useState(false);
  const [suggestionBubble, setSuggestionBubble] = useState(null);
  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const [messageSuggestions, setMessageSuggestions] = useState({});
  const [translatingMessages, setTranslatingMessages] = useState(new Set());
  const [showWritingHistoryModal, setShowWritingHistoryModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [profileModalType, setProfileModalType] = useState('user');
  const [userAvatar, setUserAvatar] = useState(null);
  const [voiceTranscript, setVoiceTranscript] = useState('');
  const [isVoiceMode, setIsVoiceMode] = useState(false); // 新增：语音输入模式状态
  const [selectedImages, setSelectedImages] = useState([]); // 新增：选中的图片
  const suggestionTimeoutRef = useRef(null);
  const headerTimeoutRef = useRef(null);
  const inputRef = useRef(null);
  const voiceInputRef = useRef(null); // 新增：语音输入按钮的引用
  const isSendingViaTab = useRef(false);

  useEffect(() => {
    // Listen for auth state changes
    const unsubscribe = onAuthChange((user) => {
      if (user) {
        setUser(user);
        setShowAuthModal(false); // Close auth modal if user logs in
      } else {
        setUser(null);
      }
    });
    return () => unsubscribe(); // Unsubscribe on cleanup
  }, []);

  // 延迟加载建议缓存，避免阻塞初始渲染
  useEffect(() => {
    const loadSuggestions = () => {
      try {
        const savedSuggestions = localStorage.getItem('current_chat_suggestions');
        if (savedSuggestions) {
          setMessageSuggestions(JSON.parse(savedSuggestions));
        }
      } catch (error) {
        console.error('加载建议缓存失败:', error);
      }
    };

    // 使用setTimeout延迟加载，让页面先渲染
    const timeoutId = setTimeout(loadSuggestions, 0);
    return () => clearTimeout(timeoutId);
  }, []);

  // 加载用户头像
  useEffect(() => {
    try {
      const saved = localStorage.getItem('user_avatar');
      if (saved) {
        const avatarData = JSON.parse(saved);
        setUserAvatar(avatarData.image);
      }
    } catch (e) {
      console.error('Failed to load user avatar:', e);
    }
  }, []);

  // 自动生成建议逻辑（但不显示，只在悬浮时显示）
  useEffect(() => {
    if (!autoShowSuggestion) return;

    // 获取最新的用户消息
    const lastUserMessage = messages.filter(msg => msg.type === 'user').pop();
    if (!lastUserMessage || messageSuggestions[lastUserMessage.id]) return;

    // 检查用户消息是否有文本内容，如果没有文本内容则跳过建议生成
    if (!lastUserMessage.content || lastUserMessage.content.trim() === '') {
      console.log('⏭️ 跳过空文本消息的建议生成:', {
        messageId: lastUserMessage.id,
        hasImages: !!(lastUserMessage.images && lastUserMessage.images.length > 0),
        contentLength: lastUserMessage.content ? lastUserMessage.content.length : 0
      });
      return;
    }

    // 延迟一点时间，确保消息已经渲染完成
    const timer = setTimeout(async () => {
      try {
        // 获取当前消息的上下文（AI回复的前一条消息）
        const currentMessageIndex = messages.findIndex(msg => msg.id === lastUserMessage.id);
        let contextMessage = null;

        // 查找当前用户消息之前的AI回复
        for (let i = currentMessageIndex - 1; i >= 0; i--) {
          if (messages[i].type === 'ai') {
            contextMessage = messages[i].content;
            break;
          }
        }

        console.log('🚀 自动生成表达建议...', {
          userMessage: lastUserMessage.content,
          hasContext: !!contextMessage
        });

        const suggestion = await getExpressionSuggestion(lastUserMessage.content, contextMessage);

        console.log('✅ 表达建议生成完成:', {
          messageId: lastUserMessage.id,
          suggestionLength: suggestion.length
        });

        setMessageSuggestions(prev => {
          const newSuggestions = { ...prev, [lastUserMessage.id]: suggestion };
          localStorage.setItem('current_chat_suggestions', JSON.stringify(newSuggestions));
          return newSuggestions;
        });
      } catch (error) {
        console.error('自动生成建议失败:', error);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [messages, autoShowSuggestion, messageSuggestions]);

  const handleSettingsClick = () => {
    if (user) {
      // If user is logged in, show the API config modal
      if (onShowApiConfig) onShowApiConfig();
    } else {
      // If user is not logged in, show the auth modal
      setShowAuthModal(true);
    }
  };



  const onSendMessage = () => {
    // 如果有图片，发送多模态消息
    if (selectedImages.length > 0) {
      handleSendMessage(inputText, selectedImages);
      setSelectedImages([]);
    } else {
      handleSendMessage(inputText);
    }
    setInputText('');
  }

  // 处理图片选择
  const handleImageSelect = (imageData) => {
    setSelectedImages(prev => [...prev, imageData]);
  };

  // 处理图片删除
  const handleImageRemove = (imageId) => {
    setSelectedImages(prev => prev.filter(img => img.id !== imageId));
  };

  // 处理文件选择（用于拖拽和粘贴）
  const handleFileSelect = (files) => {
    if (isLoading) return;

    const imageFiles = Array.from(files).filter(file =>
      file.type.startsWith('image/') && file.size <= 10 * 1024 * 1024 // 10MB限制
    );

    if (imageFiles.length === 0) {
      alert('请选择有效的图片文件（最大10MB）');
      return;
    }

    // 转换为base64
    imageFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageData = {
          id: Date.now() + Math.random(),
          file: file,
          name: file.name,
          size: file.size,
          dataUrl: e.target.result,
          type: file.type
        };
        handleImageSelect(imageData);
      };
      reader.readAsDataURL(file);
    });
  };

  const handleWordClick = (word) => {
    const cleanWord = word.replace(/[^\w]/g, '').toLowerCase();
    if (cleanWord.length > 0) {
      dispatch({ type: 'SHOW_DICTIONARY', payload: true, word: cleanWord });
    }
  };

  const renderClickableText = (text) => {
    const parts = text.split(/(\s+|[^\w\s])/);
    return parts.map((part, index) => {
      if (/\w/.test(part) && part.trim().length > 0) {
        return (
          <span
            key={index}
            onClick={() => handleWordClick(part)}
            className="clickable-word"
            title={`点击查询 "${part.replace(/[^\w]/g, '')}" 的释义`}
          >
            {part}
          </span>
        );
      }
      return <span key={index}>{part}</span>;
    });
  };

  const handleSelectChatHistory = (session) => {
    console.log('选择聊天历史记录:', session);

    // 验证数据完整性
    if (!session || !session.messages || !Array.isArray(session.messages)) {
      console.error('聊天历史记录数据无效:', session);
      alert('聊天历史记录数据无效，无法加载');
      return;
    }

    // 验证消息格式
    const validMessages = session.messages.filter(msg =>
      msg &&
      typeof msg.id === 'string' &&
      typeof msg.type === 'string' &&
      typeof msg.content === 'string'
    );

    if (validMessages.length === 0) {
      console.error('没有有效的消息数据');
      alert('聊天历史记录中没有有效的消息');
      return;
    }

    console.log('加载聊天历史记录，消息数量:', validMessages.length);
    setMessages(validMessages);
    setMessageSuggestions({});
    setSuggestionBubble(null);
    localStorage.removeItem('current_chat_suggestions');
  };

  const renderMarkdown = (text) => {
    if (!text) return '';
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    let html = '';
    let isFirstSection = true;
    lines.forEach((line) => {
      if (line.endsWith('：')) {
        const marginTop = isFirstSection ? '0px' : '8px';
        html += `<div style="font-weight: 600; margin-top: ${marginTop}; margin-bottom: 4px; color: inherit;">${line}</div>`;
        isFirstSection = false;
      } else if (line.startsWith('• ')) {
        html += `<div style="margin-left: 12px; margin-bottom: 2px; line-height: 1.3;">${line}</div>`;
      } else {
        html += `<div style="margin-bottom: 4px; line-height: 1.3;">${line}</div>`;
      }
    });
    return html;
  };

  const shareWritingToChat = (writingRecord) => {
    // Data validation to prevent crash from corrupted history
    const content = typeof writingRecord.content === 'string' ? writingRecord.content : '';
    const title = typeof writingRecord.title === 'string' ? writingRecord.title : '未命名文档';

    const contextData = {
      title: title,
      content: content,
      timestamp: writingRecord.timestamp,
      wordCount: content.split(/\s+/).length,
      sharedAt: new Date().toISOString()
    };
    setSharedWritingContext(contextData);
    localStorage.setItem('shared_writing_context', JSON.stringify(contextData));
    const systemMessage = {
      id: generateUniqueId(),
      type: 'system',
      content: `用户分享了一篇写作作品："${contextData.title}"（${contextData.wordCount}词）。请基于这篇文章的内容与用户进行更有针对性的对话。`,
      timestamp: new Date(),
      isContextShare: true
    };
    setMessages(prev => [...prev, systemMessage]);
    setShowWritingHistoryModal(false);
    setTimeout(async () => {
      try {
        const personalizedResponse = await generateWritingSharingResponse(contextData);
        const parsedResponse = parseAIResponse(personalizedResponse);
        const aiResponse = { id: generateUniqueId(), type: 'ai', content: parsedResponse.english, translation: parsedResponse.chinese, timestamp: new Date() };
        setMessages(prev => [...prev, aiResponse]);
      } catch (error) {
        console.error('生成个性化回复失败:', error);
        const fallbackResponse = { id: generateUniqueId(), type: 'ai', content: `Thanks for sharing your writing "${contextData.title}" with me! 📝 I can see you put a lot of thought into it. I'd love to discuss your ideas, help you refine your expressions, or explore the topics you wrote about. What aspect would you like to focus on?`, translation: `谢谢你和我分享你的作品《${contextData.title}》！📝 我能看出你投入了很多思考。我很乐意讨论你的想法，帮你完善表达，或者探讨你写的话题。你想重点关注哪个方面呢？`, timestamp: new Date() };
        setMessages(prev => [...prev, fallbackResponse]);
      }
    }, 1000);
  };

  const clearWritingContext = () => {
    setSharedWritingContext(null);
    localStorage.removeItem('shared_writing_context');
    const clearMessage = { id: generateUniqueId(), type: 'ai', content: "Got it! I've cleared the writing context. We can now chat about anything you'd like! 😊", translation: "明白了！我已经清除了写作上下文。现在我们可以聊任何你想聊的话题！😊", timestamp: new Date() };
    setMessages(prev => [...prev, clearMessage]);
  };

  const handleAvatarClick = (type) => {
    setProfileModalType(type);
    setShowProfileModal(true);
  };

  const handleAvatarChange = (newAvatar) => {
    setUserAvatar(newAvatar);
  };

  const handleShareWritingFromProfile = () => {
    setShowWritingHistoryModal(true);
  };

  // 处理与日记聊天
  const handleChatWithDiary = (diary) => {
    // 关闭个人信息模态框
    setShowProfileModal(false);

    // 创建基于日记的上下文消息
    const diaryContextMessage = {
      id: generateUniqueId(),
      type: 'user',
      content: `I'd like to discuss this diary entry from ${diary.date}:\n\n"${diary.english}"\n\nCan you help me explore this topic further?`,
      timestamp: new Date(),
      isFromDiary: true,
      diaryDate: diary.date
    };

    // 添加到消息列表并自动发送
    setMessages(prev => [...prev, diaryContextMessage]);

    // 自动发送消息给AI
    setTimeout(() => {
      handleSendMessage(diaryContextMessage.content);
    }, 100);
  };

  // 手动翻译AI消息
  const handleTranslateMessage = async (messageId) => {
    if (translatingMessages.has(messageId)) return;

    setTranslatingMessages(prev => new Set([...prev, messageId]));

    try {
      const message = messages.find(msg => msg.id === messageId);
      if (!message || !message.content) return;

      const translation = await translateMessage(message.content);

      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, translation }
          : msg
      ));
    } catch (error) {
      console.error('翻译失败:', error);
    } finally {
      setTranslatingMessages(prev => {
        const newSet = new Set(prev);
        newSet.delete(messageId);
        return newSet;
      });
    }
  };

  const handleUserMessageClick = async (message, event) => {
    if (isLoadingSuggestion) return;
    if (suggestionTimeoutRef.current) {
      clearTimeout(suggestionTimeoutRef.current);
    }
    const rect = event.currentTarget.getBoundingClientRect();
    // 计算建议气泡位置：在用户消息下方，与用户消息左对齐
    const position = {
      x: rect.left, // 直接与用户消息左边缘对齐
      y: rect.bottom + 15
    };
    if (messageSuggestions[message.id]) {
      setSuggestionBubble({ messageId: message.id, content: messageSuggestions[message.id], position, isLoading: false });
      return;
    }
    setIsLoadingSuggestion(true);
    setSuggestionBubble({ messageId: message.id, content: '正在生成建议...', position, isLoading: true });

    try {
      // 获取当前消息的上下文（AI回复的前一条消息）
      const currentMessageIndex = messages.findIndex(msg => msg.id === message.id);
      let contextMessage = null;

      console.log('🔍 开始查找上下文...', {
        currentMessageId: message.id,
        currentMessageContent: message.content,
        currentMessageIndex: currentMessageIndex,
        totalMessages: messages.length
      });

      // 查找当前用户消息之前的AI回复
      for (let i = currentMessageIndex - 1; i >= 0; i--) {
        if (messages[i].type === 'ai') {
          contextMessage = messages[i].content;
          console.log('✅ 找到上下文消息:', {
            contextIndex: i,
            contextMessageId: messages[i].id,
            contextMessageContent: contextMessage.substring(0, 100) + '...',
            contextMessageLength: contextMessage.length
          });
          break;
        }
      }

      if (!contextMessage) {
        console.log('⚠️ 未找到上下文消息，将使用无上下文模式');
      }

      console.log('🚀 开始获取表达建议...', {
        userMessage: message.content,
        hasContext: !!contextMessage,
        contextPreview: contextMessage ? contextMessage.substring(0, 50) + '...' : '无'
      });

      const suggestion = await getExpressionSuggestion(message.content, contextMessage);

      console.log('✅ 表达建议生成完成:', {
        messageId: message.id,
        suggestionLength: suggestion.length,
        suggestionPreview: suggestion.substring(0, 100) + '...',
        usedContext: !!contextMessage
      });

      setMessageSuggestions(prev => {
        const newSuggestions = { ...prev, [message.id]: suggestion };
        localStorage.setItem('current_chat_suggestions', JSON.stringify(newSuggestions));
        return newSuggestions;
      });
      setSuggestionBubble({ messageId: message.id, content: suggestion, position, isLoading: false });
    } catch (error) {
      console.error('Failed to get suggestion:', error);
      setSuggestionBubble({ messageId: message.id, content: '获取建议失败，请重试。', position, isLoading: false });
    } finally {
      setIsLoadingSuggestion(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionBubble && !event.target.closest('.suggestion-bubble')) {
        setSuggestionBubble(null);
      }
    };
    if (suggestionBubble) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [suggestionBubble]);

  const handleHeaderMouseEnter = () => {
    if (headerTimeoutRef.current) {
      clearTimeout(headerTimeoutRef.current);
    }
    setShowFloatingHeader(true);
  };

  const handleHeaderMouseLeave = () => {
    headerTimeoutRef.current = setTimeout(() => {
      setShowFloatingHeader(false);
    }, 300);
  };

  // 处理语音输入转录变化
  const handleVoiceTranscriptChange = (transcript) => {
    setVoiceTranscript(transcript);
  };

  // 处理语音输入完成
  const handleVoiceFinalTranscript = (finalTranscript) => {
    // 如果是通过 TAB 键触发的停止，则不执行任何操作，因为 TAB 键处理器已经处理了发送和清空
    if (isSendingViaTab.current) {
      isSendingViaTab.current = false; // 重置标志
      return;
    }

    if (finalTranscript.trim()) {
      // 当语音识别自然结束时，将最终文本更新到输入框
      const newText = inputText ? `${inputText} ${finalTranscript}`.trim() : finalTranscript;
      setInputText(newText);
    }
    // 清空实时语音记录并退出语音模式
    setVoiceTranscript('');
    setIsVoiceMode(false);
  };

  // 处理 TAB 键按下
  const handleTabKeyPress = () => {
    if (isVoiceMode) {
      // 如果在语音模式下，按 TAB 表示停止录音并发送
      isSendingViaTab.current = true; // 设置标志，以防止 handleVoiceFinalTranscript 冲突
      if (voiceInputRef.current) {
        voiceInputRef.current.stopListening();
      }
      const textToSend = (voiceTranscript ? `${inputText}${voiceTranscript}` : inputText).trim();
      if (textToSend) {
        handleSendMessage(textToSend);
        setInputText('');
      }
      setIsVoiceMode(false);
      setVoiceTranscript('');
    } else {
      // 如果不在语音模式下，按 TAB 表示开始录音
      // 保留输入框中已有的文本
      setIsVoiceMode(true);
      if (voiceInputRef.current) {
        voiceInputRef.current.startListening();
      }
    }
  };

  return (
    <div
      className="h-screen w-full transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? '#1A1611' : '#F5EFE6' }}
    >
      <div
        className="fixed top-0 left-0 right-0 z-40"
        style={{ height: '20px' }}
        onMouseEnter={handleHeaderMouseEnter}
        onMouseLeave={handleHeaderMouseLeave}
      />

      <div
        className="fixed top-0 left-0 right-0 z-50 transition-all duration-300 border-b"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
          borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
          transform: showFloatingHeader ? 'translateY(0)' : 'translateY(-100%)',
          boxShadow: showFloatingHeader
            ? (isDarkMode ? '0 4px 12px rgba(0, 0, 0, 0.3)' : '0 4px 12px rgba(93, 64, 55, 0.15)')
            : 'none'
        }}
        onMouseEnter={handleHeaderMouseEnter}
        onMouseLeave={handleHeaderMouseLeave}
      >
        <div className="max-w-7xl mx-auto py-6">
          <div className="flex items-center justify-between" style={{ paddingLeft: '80px', paddingRight: '80px' }}>
            <div className="flex items-center gap-8">
              <div>
                <h1
                  className="text-3xl font-bold transition-colors duration-300"
                  style={{
                    color: isDarkMode ? '#E8DCC6' : '#5D4037',
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    letterSpacing: '0.1em'
                  }}
                >
                  JustTalk
                </h1>
              </div>
              <div
                className="flex items-center justify-center w-12 h-12"
              >
                <img
                  src={isDarkMode ? "/logo-dark.svg" : "/logo.svg"}
                  alt="JustTalk"
                  className="w-32 h-32 object-contain"
                  style={{ maxWidth: 'none', maxHeight: 'none' }}
                />
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={() => {
                  console.log('聊天历史按钮被点击');
                  setShowChatHistory(true);
                }}
                className="header-btn"
                title="聊天历史"
              >
                <History className="w-6 h-6" />
              </button>
              <button
                onClick={() => {
                  console.log('日记按钮被点击');
                  handleAvatarClick('alex');
                }}
                className="header-btn"
                title="Alex的日记"
              >
                <BookOpen className="w-6 h-6" />
              </button>
              <button
                onClick={handleSettingsClick}
                className="header-btn"
                title="Settings"
              >
                <Settings className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="max-w-4xl mx-auto flex flex-col h-full">
        <div
          className="flex-1 overflow-y-auto p-6 pt-4 space-y-4 chat-scrollbar"
          style={{ minHeight: 'calc(100vh - 200px)' }}
        >
          {sharedWritingContext && (
            <div
              className="mb-4 p-4 rounded-xl border transition-all duration-300"
              style={{
                backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
                borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                color: isDarkMode ? '#E8DCC6' : '#5D4037'
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FileText className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  <div>
                    <div
                      className="font-medium"
                      style={{
                        fontFamily: 'Georgia, "Noto Serif SC", serif',
                        fontSize: '16px'
                      }}
                    >
                      写作上下文：{sharedWritingContext.title}
                    </div>
                    <div
                      className="text-sm mt-1"
                      style={{
                        color: isDarkMode ? '#C4B59A' : '#8B4513',
                        fontFamily: 'Georgia, "Noto Serif SC", serif'
                      }}
                    >
                      {sharedWritingContext.wordCount} 词 • {new Date(sharedWritingContext.sharedAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <button
                  onClick={clearWritingContext}
                  className="p-2 rounded-lg transition-colors duration-200"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    backgroundColor: 'transparent'
                  }}
                  title="清除写作上下文"
                >
                  ✕
                </button>
              </div>
            </div>
          )}

          {messages.filter(message => message.type !== 'system').map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} items-start gap-3`}
            >
              {message.type === 'ai' && (
                <div
                  className="chat-avatar alex-avatar flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer"
                  title="点击查看 Alex 的个人信息"
                  onClick={() => handleAvatarClick('alex')}
                >
                  <span style={{ fontSize: '20px' }}>🌿</span>
                </div>
              )}

              <div className="relative flex flex-col">
                <div
                  data-message-id={message.id}
                  className="max-w-xs lg:max-w-md px-4 py-3 rounded-2xl transition-all duration-300 cursor-pointer chat-message-text"
                  style={{
                    backgroundColor: message.type === 'user'
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#332B22' : '#FFFEF7'),
                    color: message.type === 'user'
                      ? '#FEFCF5'
                      : (isDarkMode ? '#E8DCC6' : '#5D4037'),
                    lineHeight: '1.6',
                    border: message.type === 'ai' ? `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}` : 'none',
                    transform: hoveredMessage === message.id && message.type === 'ai' ? 'scale(1.02)' : 'scale(1)',
                    opacity: message.type === 'user' && isLoadingSuggestion ? 0.7 : 1,
                    position: 'relative',
                    borderRadius: message.type === 'user' ? '20px 20px 4px 20px' : '20px 20px 20px 4px'
                  }}
                  onMouseEnter={(e) => {
                    if (suggestionTimeoutRef.current) {
                      clearTimeout(suggestionTimeoutRef.current);
                    }
                    if (message.type === 'ai') {
                      setHoveredMessage(message.id);
                    } else if (message.type === 'user' && messageSuggestions[message.id]) {
                      const rect = e.currentTarget.getBoundingClientRect();
                      const position = {
                        x: rect.left, // 直接与用户消息左边缘对齐
                        y: rect.bottom + 15
                      };
                      setSuggestionBubble({ messageId: message.id, content: messageSuggestions[message.id], position, isLoading: false });
                    }
                  }}
                  onMouseLeave={() => {
                    setHoveredMessage(null);
                    if (message.type === 'user' && messageSuggestions[message.id] && suggestionBubble?.messageId === message.id) {
                      suggestionTimeoutRef.current = setTimeout(() => {
                        setSuggestionBubble(null);
                      }, 300);
                    }
                  }}
                  onClick={(e) => {
                    if (message.type === 'user' && !autoShowSuggestion) {
                      handleUserMessageClick(message, e);
                    }
                  }}
                  title={
                    message.type === 'user'
                      ? messageSuggestions[message.id]
                        ? ''
                        : autoShowSuggestion
                          ? '自动显示建议中...'
                          : '点击获取表达建议'
                      : ''
                  }
                >
                  <div>
                    {/* 图片显示 */}
                    {message.images && message.images.length > 0 && (
                      <div className="mb-3">
                        <div className="flex flex-wrap gap-2">
                          {message.images.map((image, index) => (
                            <div
                              key={image.id || index}
                              className="message-image"
                              style={{
                                position: 'relative',
                                maxWidth: '200px',
                                maxHeight: '200px',
                                borderRadius: '8px',
                                overflow: 'hidden',
                                border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
                                backgroundColor: isDarkMode ? '#2D2A24' : '#FEFCF5'
                              }}
                            >
                              <img
                                src={image.dataUrl}
                                alt={image.name || `图片 ${index + 1}`}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                  cursor: 'pointer'
                                }}
                                onClick={() => {
                                  // 点击图片可以放大查看
                                  window.open(image.dataUrl, '_blank');
                                }}
                                title="点击放大查看"
                              />
                              <div
                                className="image-info"
                                style={{
                                  position: 'absolute',
                                  bottom: '0',
                                  left: '0',
                                  right: '0',
                                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                  color: 'white',
                                  fontSize: '10px',
                                  padding: '2px 4px',
                                  textAlign: 'center'
                                }}
                              >
                                {image.name || `图片 ${index + 1}`}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 文本内容 */}
                    {message.content && (
                      <div className="inline">
                        {message.type === 'ai' ? renderClickableText(message.content) : message.content}
                      </div>
                    )}

                    {message.type === 'user' && messageSuggestions[message.id] && (
                      <span
                        className="inline-block ml-2 opacity-60"
                        style={{ fontSize: '12px', color: '#FEFCF5' }}
                        title="已有表达建议"
                      >
                        💡
                      </span>
                    )}
                    {message.type === 'ai' && (
                      <span className="inline-block ml-2 align-bottom">
                        <div className="flex items-center gap-1">
                          <VoicePlayButton
                            text={message.content}
                            isDarkMode={isDarkMode}
                            size="small"
                          />
                          {!message.translation && (
                            <button
                              onClick={() => handleTranslateMessage(message.id)}
                              disabled={translatingMessages.has(message.id)}
                              className={`translate-btn ${translatingMessages.has(message.id) ? 'loading' : ''}`}
                              title={translatingMessages.has(message.id) ? '翻译中...' : '点击翻译'}
                            >
                              {translatingMessages.has(message.id) ? (
                                <div
                                  className="animate-spin rounded-full border-2 border-current border-t-transparent"
                                  style={{ width: '16px', height: '16px' }}
                                />
                              ) : (
                                <span style={{ fontSize: '14px' }}>🌐</span>
                              )}
                            </button>
                          )}
                        </div>
                      </span>
                    )}
                  </div>
                </div>
                {message.type === 'ai' && message.translation && (hoveredMessage === message.id) && (
                  <div
                    className="absolute z-10 px-4 py-3 rounded-xl shadow-lg transition-all duration-200"
                    style={{
                      backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      lineHeight: '1.5',
                      maxWidth: '400px',
                      minWidth: '250px',
                      top: '0',
                      left: '100%',
                      marginLeft: '12px',
                      boxShadow: isDarkMode
                        ? '0 4px 12px rgba(0, 0, 0, 0.3)'
                        : '0 4px 12px rgba(93, 64, 55, 0.15)'
                    }}
                  >
                    {message.translation}
                    <div
                      style={{
                        position: 'absolute',
                        top: '16px',
                        left: '-6px',
                        width: '12px',
                        height: '12px',
                        backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
                        border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
                        borderTop: 'none',
                        borderRight: 'none',
                        transform: 'rotate(45deg)'
                      }}
                    />
                  </div>
                )}
              </div>

              {message.type === 'user' && (
                <div
                  className="chat-avatar user-avatar flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer overflow-hidden"
                  title="点击查看个人信息和分享写作"
                  onClick={() => handleAvatarClick('user')}
                  style={{
                    backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                    border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  {userAvatar ? (
                    <img
                      src={userAvatar}
                      alt="用户头像"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span style={{ fontSize: '18px' }}>👤</span>
                  )}
                </div>
              )}
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start items-start gap-3">
              <div
                className="chat-avatar alex-avatar flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300"
              >
                <span style={{ fontSize: '20px' }}>🌿</span>
              </div>
              <div
                className="max-w-xs lg:max-w-md px-4 py-3 rounded-2xl transition-colors duration-300"
                style={{
                  backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
                  borderRadius: '20px 20px 20px 4px'
                }}
              >
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534' }}></div>
                    <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.4s' }}></div>
                  </div>
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        <div className="p-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleNewConversation}
              disabled={isLoading}
              className="new-chat-btn"
              title="开始新对话"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </button>

            <div className="flex-1 relative">
              <div className="relative">
                <textarea
                  ref={inputRef}
                  value={voiceTranscript ? `${inputText}${voiceTranscript}` : inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      onSendMessage();
                    } else if (e.key === 'Tab') {
                      e.preventDefault();
                      handleTabKeyPress();
                    }
                  }}
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.currentTarget.style.borderColor = isDarkMode ? '#D2691E' : '#166534';
                    e.currentTarget.style.backgroundColor = isDarkMode ? '#2D2A24' : '#F0F4F0';
                  }}
                  onDragLeave={(e) => {
                    e.currentTarget.style.borderColor = isDarkMode ? '#4A3F35' : '#E6D7B8';
                    e.currentTarget.style.backgroundColor = isDarkMode ? '#332B22' : '#FFFEF7';
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    e.currentTarget.style.borderColor = isDarkMode ? '#4A3F35' : '#E6D7B8';
                    e.currentTarget.style.backgroundColor = isDarkMode ? '#332B22' : '#FFFEF7';

                    const files = e.dataTransfer.files;
                    if (files && files.length > 0) {
                      handleFileSelect(files);
                    }
                  }}
                  onPaste={(e) => {
                    const items = e.clipboardData.items;
                    const files = [];

                    for (let i = 0; i < items.length; i++) {
                      const item = items[i];
                      if (item.type.startsWith('image/')) {
                        const file = item.getAsFile();
                        if (file) {
                          files.push(file);
                        }
                      }
                    }

                    if (files.length > 0) {
                      e.preventDefault();
                      handleFileSelect(files);
                    }
                  }}
                  placeholder={isVoiceMode ? "按 TAB 停止录音并发送..." : "Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)"}
                  className="w-full resize-none rounded-xl focus:outline-none chat-input-scrollbar"
                  style={{
                    backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                    color: isDarkMode ? '#E8DCC6' : '#5D4037',
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    padding: '16px 60px 16px 20px', // 右侧留出空间给语音按钮
                    fontSize: '16px',
                    border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
                    minHeight: '48px',
                    maxHeight: '120px',
                    lineHeight: '1.5',
                    transition: 'background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease'
                  }}
                  rows={2}
                />

                {/* 选中的图片预览 - 在输入框内部 */}
                {selectedImages.length > 0 && (
                  <div className="absolute bottom-2 left-2 flex flex-wrap gap-1 z-10">
                    {selectedImages.map((image) => (
                      <div
                        key={image.id}
                        className="image-preview"
                        style={{
                          position: 'relative',
                          width: '40px',
                          height: '40px',
                          borderRadius: '4px',
                          overflow: 'hidden',
                          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
                          backgroundColor: isDarkMode ? '#1A1A1A' : '#FEFCF5',
                          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                        }}
                      >
                        <img
                          src={image.dataUrl}
                          alt={image.name}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                        />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleImageRemove(image.id);
                          }}
                          className="remove-image-btn"
                          style={{
                            position: 'absolute',
                            top: '-2px',
                            right: '-2px',
                            width: '14px',
                            height: '14px',
                            borderRadius: '50%',
                            backgroundColor: 'rgba(220, 38, 38, 0.9)',
                            color: 'white',
                            border: 'none',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '8px',
                            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                          }}
                          title="删除图片"
                        >
                          <X className="w-2 h-2" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {/* 语音输入按钮 */}
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <VoiceInputButton
                    ref={voiceInputRef}
                    onTranscriptChange={handleVoiceTranscriptChange}
                    onFinalTranscript={handleVoiceFinalTranscript}
                    isDarkMode={isDarkMode}
                    disabled={isLoading}
                    alwaysVisible={true}
                  />
                </div>
              </div>
            </div>
            <button
              onClick={onSendMessage}
              disabled={(!inputText.trim() && selectedImages.length === 0) || isLoading}
              className="send-btn"
              aria-label="send"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>



      {suggestionBubble && (
        <div
          className="suggestion-bubble custom-scrollbar fixed z-50 max-w-sm p-4 rounded-xl shadow-lg transition-all duration-200"
          onMouseEnter={() => {
            if (suggestionTimeoutRef.current) {
              clearTimeout(suggestionTimeoutRef.current);
            }
          }}
          onMouseLeave={() => {
            suggestionTimeoutRef.current = setTimeout(() => {
              setSuggestionBubble(null);
            }, 300);
          }}
          style={{
            left: `${suggestionBubble.position.x}px`,
            top: `${suggestionBubble.position.y}px`,
            transform: 'translateY(0)',
            backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            fontSize: '14px',
            lineHeight: '1.5',
            boxShadow: isDarkMode
              ? '0 8px 24px rgba(0, 0, 0, 0.4)'
              : '0 8px 24px rgba(93, 64, 55, 0.2)',
            maxHeight: '300px',
            overflowY: 'auto'
          }}
        >
          <div className="flex items-start justify-between mb-2">
            <div
              className="text-xs font-medium"
              style={{ color: isDarkMode ? '#D2691E' : '#166534' }}
            >
              表达建议
            </div>
            <button
              onClick={() => setSuggestionBubble(null)}
              className="ml-2 text-xs opacity-60 hover:opacity-100 transition-opacity"
              style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
            >
              ✕
            </button>
          </div>

          <div>
            {suggestionBubble.isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534' }}></div>
                <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534', animationDelay: '0.4s' }}></div>
                <span className="text-sm">正在生成建议...</span>
              </div>
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  __html: renderMarkdown(suggestionBubble.content)
                }}
                style={{
                  color: isDarkMode ? '#E8DCC6' : '#5D4037'
                }}
              />
            )}
          </div>
        </div>
      )}

      {showWritingHistoryModal && (
        <WritingHistoryModal
          isOpen={showWritingHistoryModal}
          onClose={() => setShowWritingHistoryModal(false)}
          onShareWriting={shareWritingToChat}
          isDarkMode={isDarkMode}
        />
      )}

      <ProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        profileType={profileModalType}
        isDarkMode={isDarkMode}
        onShareWriting={handleShareWritingFromProfile}
        autoPlayTTS={autoPlayTTS}
        onChatWithDiary={handleChatWithDiary}
        onAvatarChange={handleAvatarChange}
      />

      <ChatHistoryModal
        isOpen={showChatHistory}
        onClose={() => setShowChatHistory(false)}
        onSelectSession={handleSelectChatHistory}
        isDarkMode={isDarkMode}
      />

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        isDarkMode={isDarkMode}
      />



      {/* 边缘导航箭头 - 返回写作模式 */}
      <ChatEdgeNavigationArrow
        onBackToEditor={onBackToEditor}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

export default EnglishChatPage;
