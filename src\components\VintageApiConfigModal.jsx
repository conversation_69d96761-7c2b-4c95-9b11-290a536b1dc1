import { BookOpen, Crown, Globe, LogOut, MessageCircle, Moon, Sun, User, Volume2, X, Zap } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useAppContext } from '../context/AppContext';
import {
  checkApiUsageLimit,
  getUserSettings,
  saveChatSettings,
  saveThemeSettings,
  saveVoiceSettings
} from '../services/user/userSettingsService';

const VintageApiConfigModal = ({ isOpen, onClose, onSave, isDarkMode, onToggleDarkMode, autoPlayTTS, onToggleAutoPlayTTS, aiResponseSound, onToggleAIResponseSound, dictionaryService, onDictionaryServiceChange, autoShowTranslation, onToggleAutoShowTranslation, autoShowSuggestion, onToggleAutoShowSuggestion, user, onLogout }) => {
  const { state, dispatch } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [usageInfo, setUsageInfo] = useState(null);

  useEffect(() => {
    if (isOpen && user) {
      loadUserSettings();
    } else if (isOpen && !user) {
      setUsageInfo(null);
    }
  }, [isOpen, user]);


  const loadUserSettings = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (user) {
        const [settings, usage] = await Promise.all([
          getUserSettings(user.uid),
          checkApiUsageLimit(user.uid)
        ]);
        setUsageInfo(usage);

        // 同步Firebase设置到本地状态
        console.log('📥 从Firebase加载设置:', {
          firebase: {
            autoShowTranslation: settings.autoShowTranslation,
            autoShowSuggestion: settings.autoShowSuggestion
          },
          local: {
            autoShowTranslation,
            autoShowSuggestion
          }
        });

        if (settings.autoShowTranslation !== undefined && settings.autoShowTranslation !== autoShowTranslation) {
          console.log('🔄 同步自动显示翻译设置:', {
            from: autoShowTranslation,
            to: settings.autoShowTranslation
          });
          dispatch({ type: 'TOGGLE_AUTO_SHOW_TRANSLATION' });
        }
        if (settings.autoShowSuggestion !== undefined && settings.autoShowSuggestion !== autoShowSuggestion) {
          console.log('🔄 同步自动显示纠错设置:', {
            from: autoShowSuggestion,
            to: settings.autoShowSuggestion
          });
          dispatch({ type: 'TOGGLE_AUTO_SHOW_SUGGESTION' });
        }
      }
    } catch (err) {
      console.error('加载用户设置失败:', err);
      setError('加载设置失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (user) {
        // 如果用户已登录，保存设置到Firebase
        await saveThemeSettings(user.uid, isDarkMode ? 'dark' : 'light');
        await saveVoiceSettings(user.uid, autoPlayTTS, aiResponseSound);
        await saveChatSettings(user.uid, autoShowTranslation, autoShowSuggestion);
      }

      // API Key现在由系统统一管理，不需要用户保存
      onSave(''); // 传递空字符串，因为API Key由系统管理
      onClose();
    } catch (err) {
      console.error('保存设置失败:', err);
      setError('保存设置失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackdropClick = (e) => {
    // 只有点击遮罩层本身时才关闭，点击弹窗内容不关闭
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleThemeToggle = async () => {
    try {
      onToggleDarkMode();

      if (user) {
        // 如果用户已登录，同步到Firebase
        await saveThemeSettings(user.uid, !isDarkMode ? 'dark' : 'light');
      }
    } catch (err) {
      console.error('保存主题设置失败:', err);
      // 即使Firebase保存失败，也继续本地切换
    }
  };

  const handleAutoPlayTTSToggle = async () => {
    try {
      onToggleAutoPlayTTS();

      if (user) {
        // 如果用户已登录，同步到Firebase
        await saveVoiceSettings(user.uid, !autoPlayTTS, aiResponseSound);
      }
    } catch (err) {
      console.error('保存语音设置失败:', err);
      // 即使Firebase保存失败，也继续本地切换
    }
  };

  const handleAIResponseSoundToggle = async () => {
    try {
      onToggleAIResponseSound();

      if (user) {
        // 如果用户已登录，同步到Firebase
        await saveVoiceSettings(user.uid, autoPlayTTS, !aiResponseSound);
      }
    } catch (err) {
      console.error('保存语音设置失败:', err);
      // 即使Firebase保存失败，也继续本地切换
    }
  };

  const handleAutoShowTranslationToggle = async () => {
    try {
      console.log('🔄 切换自动显示翻译:', {
        current: autoShowTranslation,
        new: !autoShowTranslation,
        userId: user?.uid
      });

      onToggleAutoShowTranslation();

      if (user) {
        // 如果用户已登录，同步到Firebase
        console.log('💾 保存到Firebase:', {
          autoShowTranslation: !autoShowTranslation,
          autoShowSuggestion
        });
        await saveChatSettings(user.uid, !autoShowTranslation, autoShowSuggestion);
        console.log('✅ Firebase保存成功');
      }
    } catch (err) {
      console.error('❌ 保存聊天设置失败:', err);
      // 即使Firebase保存失败，也继续本地切换
    }
  };

  const handleAutoShowSuggestionToggle = async () => {
    try {
      console.log('🔄 切换自动显示纠错:', {
        current: autoShowSuggestion,
        new: !autoShowSuggestion,
        userId: user?.uid
      });

      onToggleAutoShowSuggestion();

      if (user) {
        // 如果用户已登录，同步到Firebase
        console.log('💾 保存到Firebase:', {
          autoShowTranslation,
          autoShowSuggestion: !autoShowSuggestion
        });
        await saveChatSettings(user.uid, autoShowTranslation, !autoShowSuggestion);
        console.log('✅ Firebase保存成功');
      }
    } catch (err) {
      console.error('❌ 保存聊天设置失败:', err);
      // 即使Firebase保存失败，也继续本地切换
    }
  };


  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.6)' : 'rgba(93, 64, 55, 0.4)' }}
      onClick={handleBackdropClick}
      data-testid="modal-backdrop"
    >
      <div className="rounded-2xl max-w-md w-full mx-8 transition-colors duration-300" style={{
        backgroundColor: isDarkMode ? '#332B22' : '#FEFCF5'
      }}>
        <div className="flex items-center justify-between" style={{
          padding: '32px 32px 24px 32px'
        }}>
          <h3 className="text-xl font-semibold transition-colors duration-300" style={{
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            letterSpacing: '0.05em'
          }}>设置</h3>
          <button
            onClick={onClose}
            className="header-btn"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div style={{ padding: '0 32px 32px 32px' }}>
          {/* 错误提示 */}
          {error && (
            <div style={{
              backgroundColor: isDarkMode ? 'rgba(210, 105, 30, 0.15)' : 'rgba(185, 28, 28, 0.1)',
              color: isDarkMode ? '#D2691E' : '#B91C1C',
              padding: '12px 16px',
              borderRadius: '8px',
              marginBottom: '20px',
              fontSize: '14px',
              border: `1px solid ${isDarkMode ? 'rgba(210, 105, 30, 0.3)' : 'rgba(185, 28, 28, 0.2)'}`,
              fontFamily: 'Georgia, "Noto Serif SC", serif'
            }}>
              {error}
            </div>
          )}



          {/* API 使用量信息 - 紧凑版 */}
          {user && usageInfo && (
            <div style={{ marginBottom: '24px' }}>
              <div style={{
                backgroundColor: isDarkMode ? '#2A241D' : '#FFFEF7',
                border: `1px solid ${isDarkMode ? '#4A3F35' : '#E5E7EB'}`,
                borderRadius: '8px',
                padding: '12px 16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                {/* 左侧：图标 + 用户类型 + 进度条 */}
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    {usageInfo.isPremiumUser ? (
                      <Crown className="w-4 h-4" style={{ color: '#FFD700' }} />
                    ) : (
                      <Zap className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                    )}
                    <span style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      {usageInfo.isPremiumUser ? '高级用户' : '免费用户'}
                    </span>
                  </div>

                  {/* 紧凑进度条 */}
                  <div style={{
                    width: '120px',
                    height: '6px',
                    backgroundColor: isDarkMode ? '#4A3F35' : '#E5E7EB',
                    borderRadius: '3px',
                    overflow: 'hidden',
                    position: 'relative'
                  }}>
                    <div style={{
                      width: `${((usageInfo.maxRequests - usageInfo.remainingRequests) / usageInfo.maxRequests) * 100}%`,
                      height: '100%',
                      backgroundColor: usageInfo.remainingRequests > 10
                        ? (isDarkMode ? '#166534' : '#22C55E')
                        : (isDarkMode ? '#D2691E' : '#EF4444'),
                      transition: 'all 0.3s ease'
                    }} />
                  </div>
                </div>

                {/* 右侧：剩余次数 */}
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    fontSize: '13px',
                    fontWeight: '500'
                  }}>
                    {usageInfo.remainingRequests}/{usageInfo.maxRequests}
                  </span>

                  {/* 警告图标（仅在需要时显示） */}
                  {usageInfo.remainingRequests <= 10 && !usageInfo.isPremiumUser && (
                    <span style={{ fontSize: '14px' }}>
                      {usageInfo.remainingRequests === 0 ? '❌' : '⚠️'}
                    </span>
                  )}
                  {usageInfo.isPremiumUser && (
                    <span style={{ fontSize: '14px' }}>✨</span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 未登录用户提示 */}
          {!user && (
            <div style={{
              backgroundColor: isDarkMode ? 'rgba(210, 105, 30, 0.15)' : 'rgba(185, 28, 28, 0.1)',
              color: isDarkMode ? '#D2691E' : '#B91C1C',
              padding: '16px 20px',
              borderRadius: '12px',
              marginBottom: '32px',
              fontSize: '14px',
              border: `1px solid ${isDarkMode ? 'rgba(210, 105, 30, 0.3)' : 'rgba(185, 28, 28, 0.2)'}`,
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              textAlign: 'center'
            }}>
              请先登录以使用 AI 分析功能和云端设置同步
            </div>
          )}

          {/* 设置选项 */}
          <div style={{ marginBottom: '32px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>

              {/* 主题切换 */}
              <div
                onClick={onToggleDarkMode}
                className="settings-option-btn"
                data-testid="theme-toggle-btn"
                style={{
                  justifyContent: 'space-between',
                  padding: '12px 16px',
                  minHeight: 'auto'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  {isDarkMode ? (
                    <Moon className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  ) : (
                    <Sun className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  )}
                  <span style={{
                    color: isDarkMode ? '#E8DCC6' : '#5D4037',
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}>
                    {isDarkMode ? '暗色模式' : '浅色模式'}
                  </span>
                </div>
              </div>

              {/* 词典服务选择 */}
              <div
                className="settings-option-btn"
                style={{
                  justifyContent: 'space-between',
                  padding: '12px 16px',
                  minHeight: 'auto'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <BookOpen className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                  <span style={{
                    color: isDarkMode ? '#E8DCC6' : '#5D4037',
                    fontFamily: 'Georgia, "Noto Serif SC", serif',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}>
                    词典服务
                  </span>
                </div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  {/* 英文字典 */}
                  <div
                    onClick={() => onDictionaryServiceChange('free_dictionary')}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      padding: '6px 10px',
                      borderRadius: '4px',
                      backgroundColor: dictionaryService === 'free_dictionary'
                        ? (isDarkMode ? 'rgba(210, 105, 30, 0.15)' : 'rgba(22, 101, 52, 0.1)')
                        : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    <div style={{
                      width: '10px',
                      height: '10px',
                      borderRadius: '50%',
                      border: `2px solid ${dictionaryService === 'free_dictionary'
                        ? (isDarkMode ? '#D2691E' : '#166534')
                        : (isDarkMode ? '#4A3F35' : '#9CA3AF')}`,
                      backgroundColor: dictionaryService === 'free_dictionary'
                        ? (isDarkMode ? '#D2691E' : '#166534')
                        : 'transparent',
                      position: 'relative'
                    }}>
                      {dictionaryService === 'free_dictionary' && (
                        <div style={{
                          width: '3px',
                          height: '3px',
                          borderRadius: '50%',
                          backgroundColor: '#FEFCF5',
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)'
                        }} />
                      )}
                    </div>
                    <span style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '12px',
                      fontWeight: '500'
                    }}>
                      英文字典
                    </span>
                  </div>

                  {/* 英汉字典 */}
                  <div
                    onClick={() => onDictionaryServiceChange('ecdict')}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      padding: '6px 10px',
                      borderRadius: '4px',
                      backgroundColor: dictionaryService === 'ecdict'
                        ? (isDarkMode ? 'rgba(210, 105, 30, 0.15)' : 'rgba(22, 101, 52, 0.1)')
                        : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    <div style={{
                      width: '10px',
                      height: '10px',
                      borderRadius: '50%',
                      border: `2px solid ${dictionaryService === 'ecdict'
                        ? (isDarkMode ? '#D2691E' : '#166534')
                        : (isDarkMode ? '#4A3F35' : '#9CA3AF')}`,
                      backgroundColor: dictionaryService === 'ecdict'
                        ? (isDarkMode ? '#D2691E' : '#166534')
                        : 'transparent',
                      position: 'relative'
                    }}>
                      {dictionaryService === 'ecdict' && (
                        <div style={{
                          width: '3px',
                          height: '3px',
                          borderRadius: '50%',
                          backgroundColor: '#FEFCF5',
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)'
                        }} />
                      )}
                    </div>
                    <span style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '12px',
                      fontWeight: '500'
                    }}>
                      英汉字典
                    </span>
                  </div>
                </div>
              </div>

              {/* 第一行：语音自动播放 + AI回复音效 */}
              <div style={{ display: 'flex', gap: '12px' }}>
                {/* 语音自动播放开关 */}
                <div
                  onClick={onToggleAutoPlayTTS}
                  className="settings-option-btn"
                  data-testid="autoplay-tts-toggle-btn"
                  style={{
                    justifyContent: 'space-between',
                    padding: '12px 16px',
                    minHeight: 'auto',
                    flex: 1,
                    alignItems: 'center'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <Volume2 className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                    <span style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      语音自动播
                    </span>
                  </div>
                  <div style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    backgroundColor: autoPlayTTS
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#4A3F35' : '#D4C4A8'),
                    position: 'relative',
                    transition: 'all 0.2s ease',
                    cursor: 'pointer',
                    flexShrink: 0
                  }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '8px',
                      backgroundColor: '#FEFCF5',
                      position: 'absolute',
                      top: '2px',
                      left: autoPlayTTS ? '22px' : '2px',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                    }} />
                  </div>
                </div>

                {/* AI回复音效开关 */}
                <div
                  onClick={onToggleAIResponseSound}
                  className="settings-option-btn"
                  data-testid="ai-response-sound-toggle-btn"
                  style={{
                    justifyContent: 'space-between',
                    padding: '12px 16px',
                    minHeight: 'auto',
                    flex: 1,
                    alignItems: 'center'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <MessageCircle className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                    <span style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      AI回复音效
                    </span>
                  </div>
                  <div style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    backgroundColor: aiResponseSound
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#4A3F35' : '#D4C4A8'),
                    position: 'relative',
                    transition: 'all 0.2s ease',
                    cursor: 'pointer',
                    flexShrink: 0
                  }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '8px',
                      backgroundColor: '#FEFCF5',
                      position: 'absolute',
                      top: '2px',
                      left: aiResponseSound ? '22px' : '2px',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                    }} />
                  </div>
                </div>
              </div>

              {/* 第二行：自动显示翻译 + 自动显示纠错建议 */}
              <div style={{ display: 'flex', gap: '12px' }}>
                {/* 自动显示翻译开关 */}
                <div
                  onClick={handleAutoShowTranslationToggle}
                  className="settings-option-btn"
                  data-testid="auto-show-translation-toggle-btn"
                  style={{
                    justifyContent: 'space-between',
                    padding: '12px 16px',
                    minHeight: 'auto',
                    flex: 1,
                    alignItems: 'center'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <Globe className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                    <span style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      自动翻译
                    </span>
                  </div>
                  <div style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    backgroundColor: autoShowTranslation
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#4A3F35' : '#D4C4A8'),
                    position: 'relative',
                    transition: 'all 0.2s ease',
                    cursor: 'pointer',
                    flexShrink: 0
                  }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '8px',
                      backgroundColor: '#FEFCF5',
                      position: 'absolute',
                      top: '2px',
                      left: autoShowTranslation ? '22px' : '2px',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                    }} />
                  </div>
                </div>

                {/* 自动显示纠错建议开关 */}
                <div
                  onClick={handleAutoShowSuggestionToggle}
                  className="settings-option-btn"
                  data-testid="auto-show-suggestion-toggle-btn"
                  style={{
                    justifyContent: 'space-between',
                    padding: '12px 16px',
                    minHeight: 'auto',
                    flex: 1,
                    alignItems: 'center'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <MessageCircle className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                    <span style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      自动纠错
                    </span>
                  </div>
                  <div style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    backgroundColor: autoShowSuggestion
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#4A3F35' : '#D4C4A8'),
                    position: 'relative',
                    transition: 'all 0.2s ease',
                    cursor: 'pointer',
                    flexShrink: 0
                  }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '8px',
                      backgroundColor: '#FEFCF5',
                      position: 'absolute',
                      top: '2px',
                      left: autoShowSuggestion ? '22px' : '2px',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                    }} />
                  </div>
                </div>
              </div>


              {/* 使用说明按钮 */}
              <button
                onClick={() => {
                  // 使用应用状态中的当前页面
                  const currentPage = state.currentPage;
                  const helpUrl = `${window.location.origin}${window.location.pathname}?page=help&from=${currentPage}`;
                  window.location.href = helpUrl;
                }}
                className="w-full py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 mb-2"
                style={{
                  backgroundColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  fontSize: '14px',
                  fontWeight: '500',
                  border: `1px solid ${isDarkMode ? '#6B5B47' : '#D4C4A8'}`,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = isDarkMode ? '#6B5B47' : '#D4C4A8';
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = isDarkMode ? '#4A3F35' : '#E6D7B8';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                }}
              >
                <BookOpen className="w-4 h-4" />
                使用说明
              </button>

              {/* 用户信息和退出登录 - 只在用户已登录时显示 */}
              {user && (
                <>
                  {/* 分隔线 */}
                  <div style={{
                    height: '1px',
                    backgroundColor: isDarkMode ? '#4A3F35' : '#E5E7EB',
                    margin: '8px 0'
                  }} />

                  {/* 用户信息和退出登录 - 合并为一行 */}
                  <div style={{
                    cursor: 'default',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    display: 'flex'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <User className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                      <div>
                        <div style={{
                          color: isDarkMode ? '#E8DCC6' : '#5D4037',
                          fontFamily: 'Georgia, "Noto Serif SC", serif',
                          fontSize: '16px',
                          fontWeight: '500'
                        }}>
                          当前用户
                        </div>
                        <div style={{
                          color: isDarkMode ? '#C4B59A' : '#8B4513',
                          fontFamily: 'Georgia, "Noto Serif SC", serif',
                          fontSize: '13px',
                          marginTop: '1px'
                        }}>
                          {user.email}
                        </div>
                      </div>
                    </div>

                    {/* 退出登录按钮 */}
                    <button
                      onClick={() => {
                        onLogout();
                        onClose();
                      }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '6px',
                        padding: '6px 12px',
                        borderRadius: '8px',

                        backgroundColor: isDarkMode ? 'rgba(210, 105, 30, 0.1)' : 'rgba(185, 28, 28, 0.05)',
                        color: isDarkMode ? '#D2691E' : '#B91C1C',
                        fontFamily: 'Georgia, "Noto Serif SC", serif',
                        fontSize: '13px',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        flexShrink: 0
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.backgroundColor = isDarkMode ? 'rgba(210, 105, 30, 0.2)' : 'rgba(185, 28, 28, 0.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.backgroundColor = isDarkMode ? 'rgba(210, 105, 30, 0.1)' : 'rgba(185, 28, 28, 0.05)';
                      }}
                    >
                      <LogOut className="w-4 h-4" />
                      退出登录
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>


        </div>
      </div>
    </div>
  );
};

export default VintageApiConfigModal;
