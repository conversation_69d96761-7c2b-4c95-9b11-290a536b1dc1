import { collection, getDocsFromCache, getDocsFromServer, limit, orderBy, query } from 'firebase/firestore';
import { BookOpen, Camera, Image, Leaf, Loader2, MessageCircle } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { db } from '../config/firebaseConfig';
import { useAppContext } from '../context/AppContext';
import { generateImageFromText, getSavedImage, saveGeneratedImage } from '../services/ai/imageGenerationService';
import { getDiaries } from '../services/writing/diaryService';
import VoicePlayButton from './VoicePlayButton';

const DiarySection = ({ isDarkMode, isInModal = false, isCompact = false, onChatWithDiary }) => {
  // const [isGenerating, setIsGenerating] = useState(false); // 移除 - 不再需要手动生成
  const [allDiaries, setAllDiaries] = useState([]);
  const [error, setError] = useState(null);
  const [hoveredDiary, setHoveredDiary] = useState(null); // { diary, position }
  const [isLoading, setIsLoading] = useState(true);
  const [firebaseConnected, setFirebaseConnected] = useState(false);
  const hoverTimeoutRef = useRef(null);

  // 加载日记数据
  useEffect(() => {
    loadDiaries();
  }, []);

  // 滚动到最后一次浏览的位置
  useEffect(() => {
    if (isInModal) {
      // 恢复滚动位置
      const savedScrollPosition = localStorage.getItem('diaryScrollPosition');
      if (savedScrollPosition) {
        setTimeout(() => {
          // 查找滚动容器（ProfileModal中的overflow-y-auto容器）
          const scrollContainer = document.querySelector('.overflow-y-auto.custom-scrollbar');
          if (scrollContainer) {
            scrollContainer.scrollTop = parseInt(savedScrollPosition);
          }
        }, 200); // 增加延迟确保DOM已渲染
      }
    }
  }, [isInModal]);

  // 保存滚动位置
  const handleScroll = useCallback(() => {
    if (isInModal) {
      const scrollContainer = document.querySelector('.overflow-y-auto.custom-scrollbar');
      if (scrollContainer) {
        localStorage.setItem('diaryScrollPosition', scrollContainer.scrollTop.toString());
      }
    }
  }, [isInModal]);

  // 添加滚动事件监听器
  useEffect(() => {
    if (isInModal) {
      const scrollContainer = document.querySelector('.overflow-y-auto.custom-scrollbar');
      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleScroll);
        return () => {
          scrollContainer.removeEventListener('scroll', handleScroll);
        };
      }
    }
  }, [isInModal, handleScroll]);

  // Firebase缓存优先获取日记函数
  const getDiariesWithCacheFirst = async (limitCount = 30) => {
    try {
      const diariesRef = collection(db, 'diaries');
      const q = query(diariesRef, orderBy('timestamp', 'desc'), limit(limitCount));

      // 首先尝试从缓存获取
      try {
        const cacheSnapshot = await getDocsFromCache(q);
        if (!cacheSnapshot.empty) {
          const cachedDiaries = cacheSnapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              date: data.date || '',
              english: data.english || '',
              chinese: data.chinese || '',
              mood: data.mood || 'neutral',
              weather: data.weather || 'unknown',
              imageUrl: data.imageUrl || null,
              imageStatus: data.imageStatus || 'pending',
              imageGeneratedAt: data.imageGeneratedAt || null,
              timestamp: data.timestamp?.toDate?.() || new Date(data.timestamp),
              type: data.type || 'auto_generated',
              createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt)
            };
          });
          console.log('⚡ 从Firebase缓存快速加载日记:', cachedDiaries.length);

          // 调试：打印缓存中最新的几条日记信息
          cachedDiaries.slice(0, 3).forEach((diary, index) => {
            console.log(`📱 缓存日记 ${index + 1}:`, {
              id: diary.id,
              date: diary.date,
              timestamp: diary.timestamp,
              englishPreview: diary.english.substring(0, 50) + '...'
            });
          });

          return { diaries: cachedDiaries, fromCache: true };
        }
      } catch (cacheError) {
        console.log('📱 缓存为空，从服务器获取...');
      }

      // 如果缓存为空，从服务器获取
      const serverSnapshot = await getDocsFromServer(q);
      const serverDiaries = serverSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          date: data.date || '',
          english: data.english || '',
          chinese: data.chinese || '',
          mood: data.mood || 'neutral',
          weather: data.weather || 'unknown',
          imageUrl: data.imageUrl || null,
          imageStatus: data.imageStatus || 'pending',
          imageGeneratedAt: data.imageGeneratedAt || null,
          timestamp: data.timestamp?.toDate?.() || new Date(data.timestamp),
          type: data.type || 'auto_generated',
          createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt)
        };
      });
      console.log('🌐 从Firebase服务器获取日记:', serverDiaries.length);

      // 调试：打印最新的几条日记信息
      serverDiaries.slice(0, 3).forEach((diary, index) => {
        console.log(`📝 日记 ${index + 1}:`, {
          id: diary.id,
          date: diary.date,
          timestamp: diary.timestamp,
          englishPreview: diary.english.substring(0, 50) + '...',
          chinesePreview: diary.chinese.substring(0, 30) + '...'
        });
      });

      return { diaries: serverDiaries, fromCache: false };

    } catch (error) {
      console.error('Firebase获取失败:', error);
      throw error;
    }
  };

  const loadDiaries = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 优先从localStorage快速加载
      const localDiaries = getDiaries();
      if (localDiaries && localDiaries.length > 0) {
        setAllDiaries(localDiaries);
        setIsLoading(false); // 立即停止loading
        console.log('⚡ 从localStorage快速加载日记:', localDiaries.length);

        // 在后台尝试从Firebase缓存获取更新数据
        setTimeout(async () => {
          try {
            const { diaries: firebaseDiaries, fromCache } = await getDiariesWithCacheFirst(30);
            setFirebaseConnected(true);
            if (firebaseDiaries.length !== localDiaries.length) {
              setAllDiaries(firebaseDiaries);
              console.log('✅ Firebase后台同步完成，数据已更新:', firebaseDiaries.length, fromCache ? '(来自缓存)' : '(来自服务器)');
            }
          } catch (error) {
            console.warn('Firebase后台同步失败，继续使用本地数据');
            setFirebaseConnected(false);
          }
        }, 1000);
        return;
      }

      // 如果没有本地数据，使用Firebase缓存优先策略
      try {
        const { diaries: firebaseDiaries, fromCache } = await getDiariesWithCacheFirst(30);
        setAllDiaries(firebaseDiaries);
        setFirebaseConnected(true);
        console.log('✅ Firebase数据加载完成:', firebaseDiaries.length, fromCache ? '(来自缓存)' : '(来自服务器)');
      } catch (firebaseError) {
        console.warn('Firebase获取失败，回退到localStorage');
        setFirebaseConnected(false);
        const localDiaries = getDiaries();
        setAllDiaries(localDiaries);
      }

    } catch (error) {
      console.error('加载日记失败:', error);
      setError('加载日记失败，请检查网络连接');
      const localDiaries = getDiaries();
      setAllDiaries(localDiaries);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 移除前端日记生成功能 - 现在只通过 Netlify 函数自动生成
  // const handleGenerateDiary = useCallback(async () => {
  //   // 功能已移除，日记现在通过 Netlify 定时函数自动生成
  // }, []);

  // 强制从Firebase服务器刷新数据
  const handleForceRefresh = useCallback(async () => {
    console.log('🔄 强制从Firebase服务器刷新日记数据...');
    setIsLoading(true);
    try {
      const { diaries: freshDiaries } = await getDiariesWithCacheFirst(30);
      setAllDiaries(freshDiaries);
      console.log('✅ 强制刷新完成，获取到', freshDiaries.length, '条日记');
    } catch (error) {
      console.error('❌ 强制刷新失败:', error);
      setError('刷新失败: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 移除生成函数暴露 - 不再需要手动生成日记
  // useEffect(() => {
  //   if (onGenerateDiary) {
  //     onGenerateDiary(handleGenerateDiary);
  //   }
  // }, [onGenerateDiary, handleGenerateDiary]);

  const handleMouseEnter = (diary, element) => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    if (diary && element) {
      const rect = element.getBoundingClientRect();
      setHoveredDiary({
        diary,
        position: {
          top: rect.top,
          left: rect.right + 12,
        }
      });
    }
  };

  const handleMouseLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setHoveredDiary(null);
    }, 100);
  };

  // 注释掉定期刷新，避免与统一存储服务冲突
  // useEffect(() => {
  //   if (firebaseConnected) {
  //     const interval = setInterval(() => {
  //       loadDiaries();
  //     }, 30000);
  //
  //     return () => clearInterval(interval);
  //   }
  // }, [firebaseConnected, loadDiaries]);

  return (
    <>
      <div
        className={`diary-section transition-all duration-300 ${isInModal ? 'rounded-xl p-4' : 'rounded-2xl p-6 mb-6 border'}`}
        style={{
          backgroundColor: isInModal ? 'transparent' : (isDarkMode ? '#2A241D' : '#F9F7F4'),
          borderColor: isInModal ? 'transparent' : (isDarkMode ? '#4A3F35' : '#E6D7B8'),
          boxShadow: isInModal ? 'none' : (isDarkMode
            ? '0 4px 12px rgba(0, 0, 0, 0.3)'
            : '0 4px 12px rgba(93, 64, 55, 0.1)')
        }}
      >
        {!isInModal && (
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div
                className="flex items-center justify-center w-10 h-10 rounded-xl"
                style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534' }}
              >
                <BookOpen className="w-5 h-5" style={{ color: '#FEFCF5' }} />
              </div>
              <div>
                <h3
                  className="text-xl font-bold"
                  style={{
                    color: isDarkMode ? '#E8DCC6' : '#5D4037',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  Alex's Daily Journal
                </h3>
                <p
                  className="text-sm"
                  style={{
                    color: isDarkMode ? '#C4B59A' : '#8B4513',
                    fontFamily: 'Georgia, "Noto Serif SC", serif'
                  }}
                >
                  植物学家的每日发现 • 每日自动更新
                  {firebaseConnected && (
                    <span className="ml-2 text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                      🔗 云端同步
                    </span>
                  )}
                  {/* 调试按钮 - 仅开发环境 */}
                  {process.env.NODE_ENV === 'development' && (
                    <button
                      onClick={handleForceRefresh}
                      className="ml-2 text-xs px-2 py-1 rounded bg-blue-500 text-white"
                      title="强制刷新Firebase数据"
                    >
                      🔄 刷新
                    </button>
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div
            className="mb-4 p-3 rounded-lg border"
            style={{
              backgroundColor: isDarkMode ? '#3D2A2A' : '#FEF2F2',
              borderColor: isDarkMode ? '#5D3A3A' : '#FECACA',
              color: isDarkMode ? '#F87171' : '#DC2626'
            }}
          >
            {error}
          </div>
        )}

        {/* 日记列表 */}
        {isLoading ? (
          <div className="text-center py-12">
            <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
            <p style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}>正在加载日记...</p>
          </div>
        ) : allDiaries.length > 0 ? (
          <div className={`space-y-${isCompact ? '3' : '4'}`}>
            {allDiaries.map((diary, index) => (
              <DiaryItem
                key={diary.id}
                diary={diary}
                isDarkMode={isDarkMode}
                isCompact={isCompact}
                isFirst={index === 0}
                onChatWithDiary={onChatWithDiary}
                onMouseEnter={(e) => handleMouseEnter(diary, e.currentTarget)}
                onMouseLeave={handleMouseLeave}
              />
            ))}
          </div>
        ) : (
          <div
            className="text-center py-12"
            style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
          >
            <Leaf className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg mb-2">还没有日记记录</p>
            <p className="text-sm opacity-75">Alex的日记会自动生成，请稍后查看</p>
            {!firebaseConnected && (
              <p className="text-xs mt-2 opacity-60">⚠️ 云端连接失败，使用本地存储</p>
            )}
          </div>
        )}
      </div>

      {/* Floating Translation Card */}
      {hoveredDiary && hoveredDiary.diary.chinese && (
        <div
          className="fixed z-50 px-3 py-2 rounded-xl shadow-lg transition-opacity duration-200"
          onMouseEnter={() => handleMouseEnter(hoveredDiary.diary, null)} // Keep it visible when mouse is over the card
          onMouseLeave={handleMouseLeave}
          style={{
            backgroundColor: isDarkMode ? '#4A3F35' : '#F5F0E6',
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            border: `1px solid ${isDarkMode ? '#6B5B4F' : '#D4C4A8'}`,
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            fontSize: '14px',
            lineHeight: '1.5',
            maxWidth: '320px',
            minWidth: '200px',
            top: `${hoveredDiary.position.top}px`,
            left: `${hoveredDiary.position.left}px`,
            boxShadow: isDarkMode
              ? '0 4px 12px rgba(0, 0, 0, 0.3)'
              : '0 4px 12px rgba(93, 64, 55, 0.15)',
          }}
        >
          <div
            className="text-xs mb-1"
            style={{ color: isDarkMode ? '#C4B59A' : '#8B4513', fontWeight: '500' }}
          >
            中文翻译：
          </div>
          {hoveredDiary.diary.chinese}
        </div>
      )}
    </>
  );
};

// 单个日记项组件 - 使用React.memo优化
const DiaryItem = React.memo(({ diary, isDarkMode, isCompact, isFirst, onChatWithDiary, onMouseEnter, onMouseLeave }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [generatedImage, setGeneratedImage] = useState(null);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [imageError, setImageError] = useState(null);
  const { dispatch } = useAppContext();

  const handleWordClick = (word) => {
    const cleanWord = word.replace(/[^\w]/g, '').toLowerCase();
    if (cleanWord.length > 0) {
      dispatch({ type: 'SHOW_DICTIONARY', payload: true, word: cleanWord });
    }
  };

  const renderClickableText = (text) => {
    if (!text || typeof text !== 'string') {
      return <span>文本内容不可用</span>;
    }
    const parts = text.split(/(\s+|[^\w\s])/);
    return parts.map((part, index) => {
      if (/\w/.test(part) && part.trim().length > 0) {
        return (
          <span
            key={index}
            onClick={() => handleWordClick(part)}
            className="clickable-word"
            title={`点击查询 "${part.replace(/[^\w]/g, '')}" 的释义`}
          >
            {part}
          </span>
        );
      }
      return <span key={index}>{part}</span>;
    });
  };

  // 优先使用Firebase中的图像URL，回退到localStorage
  useEffect(() => {
    if (diary.imageUrl) {
      // Firebase中有图像URL，直接使用
      setGeneratedImage(diary.imageUrl);
      console.log('✅ 使用Firebase图像URL:', diary.imageUrl);
    } else {
      // 回退到localStorage
      const savedImage = getSavedImage(diary.id);
      if (savedImage) {
        setGeneratedImage(savedImage);
        console.log('📱 使用本地存储图像:', savedImage);
      }
    }
  }, [diary.id, diary.imageUrl]);

  const getTruncatedContent = (content) => {
    if (!content) return '';
    const words = content.split(' ');
    const halfLength = Math.ceil(words.length / 2);
    return words.slice(0, halfLength).join(' ');
  };

  const shouldTruncate = (content) => {
    if (!content) return false;
    return content.split(' ').length > 20;
  };

  const handleGenerateImage = async () => {
    if (isGeneratingImage || generatedImage) return;
    setIsGeneratingImage(true);
    setImageError(null);
    try {
      const imageUrl = await generateImageFromText(diary.english);
      setGeneratedImage(imageUrl);
      saveGeneratedImage(diary.id, imageUrl);
    } catch (error) {
      console.error('生成图像失败:', error);
      setImageError(error.message);
    } finally {
      setIsGeneratingImage(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  // 检查是否有图像（Firebase或本地）
  const hasImage = generatedImage || diary.imageUrl;
  const imageStatus = diary.imageStatus || 'pending';

  return (
    <div
      data-diary-id={diary.id}
      className={`diary-content rounded-xl border ${isCompact ? 'p-3' : 'p-4'}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      style={{
        backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
        borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
        color: isDarkMode ? '#E8DCC6' : '#5D4037',
        fontFamily: 'Georgia, "Noto Serif SC", serif',
        lineHeight: '1.7',
        fontSize: isCompact ? '14px' : '16px',
        marginBottom: isCompact ? '12px' : '16px',
        transition: 'all 0.3s ease',
      }}
    >
      <div className={`flex items-center justify-between ${isCompact ? 'mb-2' : 'mb-3'}`}>
        <div className="flex items-center gap-2">
          <Camera className="w-4 h-4" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
          <span
            className="text-sm font-medium"
            style={{ color: isDarkMode ? '#D2691E' : '#166534' }}
          >
            {formatDate(diary.date)}
          </span>
          {isFirst && (
            <span
              className="text-xs px-2 py-1 rounded-full"
              style={{
                backgroundColor: isDarkMode ? '#D2691E' : '#166534',
                color: '#FEFCF5'
              }}
            >
              Latest
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <VoicePlayButton
            text={diary.english}
            isDarkMode={isDarkMode}
            size="small"
          />

          {/* 图像状态指示器 */}
          {diary.imageUrl && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-full bg-green-500" title="图像已生成"></div>
              <span className="text-xs text-green-600">图像</span>
            </div>
          )}

          <button
            onClick={handleGenerateImage}
            disabled={isGeneratingImage || !!hasImage}
            className={`voice-play-btn w-8 h-8 ${hasImage ? 'playing' : 'default'}`}
            title={hasImage ? '图像已生成' : '生成AI图像'}
          >
            {isGeneratingImage ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Image className="w-4 h-4" />
            )}
          </button>

          {onChatWithDiary && (
            <button
              onClick={() => onChatWithDiary(diary)}
              className="voice-play-btn w-8 h-8 default"
              title="与AI讨论这篇日记"
            >
              <MessageCircle className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      <div
        className="diary-text"
        style={{
          transition: 'all 0.3s ease'
        }}
      >
        {shouldTruncate(diary.english) && !isExpanded
          ? renderClickableText(getTruncatedContent(diary.english) + '...')
          : renderClickableText(diary.english)
        }
      </div>

      {shouldTruncate(diary.english) && (
        <div
          className="mt-2 text-center"
          onClick={() => setIsExpanded(!isExpanded)}
          style={{
            cursor: 'pointer',
            color: isDarkMode ? '#D2691E' : '#166534',
            fontSize: '12px',
            opacity: 0.8,
            transition: 'opacity 0.2s ease'
          }}
          onMouseEnter={(e) => e.target.style.opacity = '1'}
          onMouseLeave={(e) => e.target.style.opacity = '0.8'}
        >
          {isExpanded ? '点击收起 ▲' : '点击展开 ▼'}
        </div>
      )}

      {/* 显示图像 */}
      {hasImage && (
        <div className="mt-4">
          <div
            className="rounded-lg overflow-hidden border"
            style={{
              borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
              maxWidth: '100%'
            }}
          >
            <img
              src={generatedImage || diary.imageUrl}
              alt="AI generated illustration for diary entry"
              className="w-full h-auto"
              style={{
                maxHeight: '400px',
                objectFit: 'contain',
                backgroundColor: isDarkMode ? '#2D2520' : '#FAF7F0'
              }}
              onError={() => {
                setImageError('图像加载失败');
                setGeneratedImage(null);
              }}
            />
          </div>

          {/* 图像状态信息 */}
          {diary.imageStatus && (
            <div className="mt-2 text-xs text-center opacity-75">
              {diary.imageStatus === 'completed' ? '✅ 图像生成完成' :
                diary.imageStatus === 'generating' ? '🔄 图像生成中...' :
                  '⏳ 等待图像生成'}
            </div>
          )}
        </div>
      )}

      {imageError && (
        <div
          className="mt-3 p-2 rounded-lg text-xs"
          style={{
            backgroundColor: isDarkMode ? '#3D2A2A' : '#FEF2F2',
            color: isDarkMode ? '#F87171' : '#DC2626',
            border: `1px solid ${isDarkMode ? '#5D3A3A' : '#FECACA'}`
          }}
        >
          图像生成失败: {imageError}
        </div>
      )}
    </div>
  );
});

export default DiarySection;
