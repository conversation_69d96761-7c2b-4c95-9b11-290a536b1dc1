const { schedule } = require('@netlify/functions');

// 每天早上 8 点（UTC 时间 0 点，对应北京时间 8 点）生成日记
const handler = schedule('0 0 * * *', async (event, context) => {
  console.log('🕐 定时任务触发：开始生成每日日记');

  try {
    // 调用日记生成函数
    const response = await fetch(`${process.env.URL}/.netlify/functions/generate-diary`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ 定时日记生成成功:', result);
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: '定时日记生成成功',
          result: result
        })
      };
    } else {
      console.error('❌ 定时日记生成失败:', response.status, response.statusText);
      return {
        statusCode: 500,
        body: JSON.stringify({
          error: '定时日记生成失败',
          status: response.status
        })
      };
    }
  } catch (error) {
    console.error('❌ 定时任务执行失败:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: '定时任务执行失败',
        details: error.message
      })
    };
  }
});

module.exports = { handler };
