import { loadPrompt } from './promptLoader.js';
import { getDoubaoApiKey } from '../user/userSettingsService.js';

const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

async function fetchFromAI(data) {
    const apiKey = await getDoubaoApiKey();
    if (!apiKey) {
        throw new Error('系统API密钥未配置，请联系管理员');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorBody}`);
    }

    const result = await response.json();
    return result.choices[0].message.content;
}

// AI建议响应函数
export const getExpressionSuggestion = async (userText, contextMessage = null) => {
    console.log('📝 表达建议服务被调用:', {
        userText: userText,
        hasContext: !!contextMessage,
        contextLength: contextMessage ? contextMessage.length : 0,
        contextPreview: contextMessage ? contextMessage.substring(0, 100) + '...' : '无'
    });
    
    const systemPrompt = await loadPrompt('expressionSuggestionPrompt');
    
    // 构建用户消息，如果有上下文则包含
    let userMessage = `请为这段英语文本提供表达改进建议："${userText}"`;
    if (contextMessage) {
        userMessage = `上下文：${contextMessage}\n\n请为这段英语文本提供表达改进建议："${userText}"`;
        console.log('✅ 已添加上下文到AI请求中');
    } else {
        console.log('ℹ️ 使用无上下文模式');
    }
    
    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userMessage }
        ],
        temperature: 2.0,
        max_tokens: 400,
        thinking: { type: "disabled" }
    };

    console.log('🚀 发送AI请求，消息长度:', userMessage.length);
    const result = await fetchFromAI(data);
    console.log('✅ AI建议生成成功，建议长度:', result.length);
    
    return result;
};